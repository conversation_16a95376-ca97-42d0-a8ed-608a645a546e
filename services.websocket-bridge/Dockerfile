FROM python:3.12-slim

WORKDIR /app

# Install Poetry
RUN pip install --no-cache-dir poetry

# Copy shared common first to leverage Docker layer caching
COPY shared/cortexa-common /app/shared/cortexa-common

# Copy service files
COPY services.websocket-bridge /app/services.websocket-bridge

WORKDIR /app/services.websocket-bridge

# Install dependencies
RUN poetry install --no-interaction --no-ansi

# Expose port
EXPOSE 8004

# Run the service
CMD ["poetry", "run", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8004"]
