from typing import Dict
from fastapi import WebSocket

from cortexacommon.logging import get_logger

logger = get_logger(__name__)


class ConnectionManager:
    def __init__(self):
        # Map operator_id -> WebSocket
        self._connections: Dict[str, WebSocket] = {}

    async def connect(self, operator_id: str, websocket: WebSocket) -> None:
        await websocket.accept()
        self._connections[operator_id] = websocket
        logger.info(f"Operator {operator_id} connected to WebSocket bridge")

    def disconnect(self, operator_id: str) -> None:
        if operator_id in self._connections:
            self._connections.pop(operator_id, None)
            logger.info(f"Operator {operator_id} disconnected from WebSocket bridge")

    async def send_to(self, operator_id: str, message: dict) -> None:
        ws = self._connections.get(operator_id)
        if not ws:
            logger.info(f"Operator {operator_id} not connected; dropping message: {message}")
            return
        await ws.send_json(message)
