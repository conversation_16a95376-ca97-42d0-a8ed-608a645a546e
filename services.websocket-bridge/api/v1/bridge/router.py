from fastapi import <PERSON><PERSON>out<PERSON>, Depends, Query, WebSocket, WebSocketDisconnect

from cortexacommon.logging import get_logger
from cortexacommon.security import TokenDecodeException

from context import ApplicationContext
from dependencies import get_app_context_dependency


logger = get_logger(__name__)
router = APIRouter(prefix="/bridge")


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: str = Query(None),
    context: ApplicationContext = Depends(get_app_context_dependency),
):
    # Authenticate
    try:
        user = context.security.authenticate(token)
        if user.app_role not in ["operator", "administrator", "manager"]:
            await websocket.close(code=4001, reason="Unauthorized")
            return
    except TokenDecodeException as e:
        logger.warning(f"WebSocket authentication failed: {e}")
        await websocket.close(code=4001, reason=f"Invalid token: {e}")
        return

    operator_id = user.sub

    await context.connection_manager.connect(operator_id, websocket)

    try:
        while True:
            await websocket.receive_text()
    except WebSocketDisconnect:
        logger.info(f"Operator {operator_id} disconnected from websocket bridge")
    finally:
        context.connection_manager.disconnect(operator_id)

