import asyncio

from cortexacommon.events.producer import EventProducer
from cortexacommon.logging import get_logger
from cortexacommon.security import Security

from config import settings
from services.connection_manager import ConnectionManager
from services.event_handler import KafkaEventRouter


logger = get_logger(__name__)


class ApplicationContext:
    _instance: "ApplicationContext | None" = None
    _lock = asyncio.Lock()

    def __init__(self):
        self._producer: EventProducer | None = None
        self._connections = ConnectionManager()
        self._security = Security(settings.security)
        self._router: KafkaEventRouter | None = None

    @classmethod
    async def get_instance(cls) -> "ApplicationContext":
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    @property
    def connection_manager(self) -> ConnectionManager:
        return self._connections

    @property
    def security(self) -> Security:
        return self._security

    async def initialize(self) -> None:
        self._router = KafkaEventRouter(
            self._connections,
            settings.kafka_settings,
            topics=["call.assigned", "webrtc.connection-details", "transcription.completed"]
        )
        await self._router.start()
        logger.info("WebSocket bridge initialized and consuming events")

    async def cleanup(self) -> None:
        if self._router:
            await self._router.stop()
            self._router = None
