COMPOSE := docker compose
SUPABASE_WORKDIR := infra


# ----- (Supabase + App) -------
.PHONY: start-dev
start-dev: supabase-start supabase-save-env up

.PHONY: stop-dev
stop-dev: down supabase-stop

.PHONY: restart-dev
restart-dev: stop-dev start-dev


# ----- (App Only) -------
.PHONY: up
up:
	@echo "Starting application services..."
	$(COMPOSE) up -d --build

.PHONY: down
down:
	@echo "Stopping application services..."
	$(COMPOSE) down


# ----- (Supabase Management) -------
.PHONY: supabase-start
supabase-start:
	@echo "Starting Supabase services..."
	npx supabase start --workdir $(SUPABASE_WORKDIR)

.PHONY: supabase-stop
supabase-stop:
	@echo "Stopping Supabase services..."
	npx supabase stop --workdir $(SUPABASE_WORKDIR)

.PHONY: supabase-save-env
supabase-save-env:
	@echo "Saving Supabase environment variables..."
	npx supabase status --workdir "$(SUPABASE_WORKDIR)" -o env | sed 's/^/SUPABASE_/' > .env.supabase.local
	sed 's/http:\/\/127.0.0.1/http:\/\/host.docker.internal/g' .env.supabase.local > .env.supabase.docker

.PHONY: supabase-reset
supabase-reset:
	@echo "Resetting local Supabase database..."
	npx supabase db reset --workdir $(SUPABASE_WORKDIR)
