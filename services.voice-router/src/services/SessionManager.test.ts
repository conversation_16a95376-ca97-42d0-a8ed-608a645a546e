import { SessionManager } from './SessionManager';
import { MediaServer } from '../core/MediaServer';
import { Session } from '../core/Session';
import { types as mediasoupTypes } from 'mediasoup';

// Mock the dependencies
jest.mock('../core/MediaServer');
jest.mock('../core/Session');

describe('SessionManager', () => {
  let mockMediaServer: jest.Mocked<MediaServer>;
  let mockSession: jest.Mocked<Session>;
  let sessionManager: SessionManager;

  beforeEach(() => {
    // Clear all mocks
    jest.clearAllMocks();

    // Create mock worker
    const mockWorker = {
      id: 'mock-worker-id',
      createRouter: jest.fn(),
    } as unknown as mediasoupTypes.Worker;

    // Create mock session
    mockSession = {
      id: 'mock-session-id',
      start: jest.fn().mockResolvedValue(undefined),
      close: jest.fn(),
    } as unknown as jest.Mocked<Session>;

    // Mock Session constructor
    (Session as jest.MockedClass<typeof Session>).mockImplementation(() => mockSession);

    // Create mock MediaServer
    mockMediaServer = {
      getWorker: jest.fn().mockReturnValue(mockWorker),
      start: jest.fn().mockResolvedValue(undefined),
    } as unknown as jest.Mocked<MediaServer>;

    // Mock MediaServer.getInstance
    (MediaServer.getInstance as jest.Mock) = jest.fn().mockReturnValue(mockMediaServer);

    sessionManager = new SessionManager(mockMediaServer);
  });

  it('should create a new session successfully', async () => {
    const sessionId = await sessionManager.createSession();

    // Verify that the session ID is not null or undefined
    expect(sessionId).toBeDefined();
    expect(sessionId).toBe('mock-session-id');

    // Verify that MediaServer.getWorker was called
    expect(mockMediaServer.getWorker).toHaveBeenCalledTimes(1);

    // Verify that Session was instantiated and started
    expect(Session).toHaveBeenCalledTimes(1);
    expect(mockSession.start).toHaveBeenCalledTimes(1);

    // Verify that the session can be retrieved
    const session = sessionManager.getSession(sessionId);
    expect(session).toBe(mockSession);
  });

  it('should delete a session successfully', async () => {
    const sessionId = await sessionManager.createSession();
    const session = sessionManager.getSession(sessionId);

    // Ensure the session exists before deletion
    expect(session).toBeDefined();
    expect(session).toBe(mockSession);

    sessionManager.deleteSession(sessionId);

    // Verify that session.close was called
    expect(mockSession.close).toHaveBeenCalledTimes(1);

    // Verify that the session is no longer retrievable
    const deletedSession = sessionManager.getSession(sessionId);
    expect(deletedSession).toBeUndefined();
  });
});