import { <PERSON><PERSON><PERSON>, Producer } from 'kafkajs';
import config from '../config';


class KafkaProducer {
  private producer: Producer;
  private isConnected = false;

  constructor() {
    const kafka = new Kafka({
      clientId: 'voice-router',
      brokers: config.kafka.brokers,
    });
    this.producer = kafka.producer();
  }

  public async connect(): Promise<void> {
    if (this.isConnected) return;
    try {
      await this.producer.connect();
      this.isConnected = true;
      console.log('Kafka Producer connected');
    } catch (error) {
      console.error('Failed to connect Kafka Producer:', error);
    }
  }

  public async disconnect(): Promise<void> {
    if (!this.isConnected) return;
    try {
      await this.producer.disconnect();
      this.isConnected = false;
      console.log('Kafka Producer disconnected');
    } catch (error) {
      console.error('Failed to disconnect Kafka Producer:', error);
    }
  }

  public async publish(topic: string, message: any): Promise<void> {
    if (!this.isConnected) {
      console.error('Kafka Producer is not connected. Cannot publish message.');
      return;
    }
    try {
      await this.producer.send({
        topic,
        messages: [{ value: JSON.stringify(message) }],
      });
    } catch (error) {
      console.error(`Failed to publish message to topic ${topic}:`, error);
    }
  }
}

export const kafkaProducer = new KafkaProducer();
