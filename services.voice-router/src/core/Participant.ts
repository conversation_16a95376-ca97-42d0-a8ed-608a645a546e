import { WebSocket } from 'ws';
import { types as mediasoupTypes } from 'mediasoup';

/**
 * Represents a single participant in a session, managing their WebSocket
 * connection and all their associated MediaSoup resources.
 */
export class Participant {
  public readonly id: string;
  private ws: WebSocket;

  // A transport is a logical entity that represents a connection between the client and the server.
  private transports = new Map<string, mediasoupTypes.WebRtcTransport>();

  // Producers and consumers are the objects that actually handle the voice.
  private producers = new Map<string, mediasoupTypes.Producer>();
  private consumers = new Map<string, mediasoupTypes.Consumer>();

  constructor(id: string, ws: WebSocket) {
    this.id = id;
    this.ws = ws;
  }

  /**
   * Returns the producers map for this participant.
   */
  public getProducers(): Map<string, mediasoupTypes.Producer> {
    return this.producers;
  }


  /**
   * Adds a new transport for this participant.
   */
  public addTransport(transport: mediasoupTypes.WebRtcTransport): void {
    this.transports.set(transport.id, transport);
  }

  /**
   * Connects a specific transport for this participant.
   *
   */
  public async connectTransport(transportId: string, dtlsParameters: mediasoupTypes.DtlsParameters): Promise<void> {
    const transport = this.transports.get(transportId);
    if (!transport) throw new Error(`Transport ${transportId} not found for participant ${this.id}`);
    await transport.connect({ dtlsParameters });
  }

  /**
   * Creates a new producer for this participant.
   * When a client starts sending voice, they first create a producer object on the server.
   */
  public async createProducer(options: {
    transportId: string;
    kind: mediasoupTypes.MediaKind;
    rtpParameters: mediasoupTypes.RtpParameters;
    appData: any;
  }): Promise<mediasoupTypes.Producer> {
    const { transportId, kind, rtpParameters, appData } = options;
    const transport = this.transports.get(transportId);
    if (!transport) throw new Error(`Transport ${transportId} not found for participant ${this.id}`);

    const producer = await transport.produce({ kind, rtpParameters, appData });
    this.producers.set(producer.id, producer);

    producer.on('transportclose', () => {
      this.producers.delete(producer.id);
    });

    return producer;
  }

  /**
   * Creates a new consumer for this participant to receive a specific producer's media.
   */
  public async createConsumer(options: {
    transportId: string;
    producerId: string;
    rtpCapabilities: mediasoupTypes.RtpCapabilities;
  }): Promise<mediasoupTypes.Consumer> {
    const { transportId, producerId, rtpCapabilities } = options;
    const transport = this.transports.get(transportId);
    if (!transport) throw new Error(`Transport ${transportId} not found for participant ${this.id}`);

    const consumer = await transport.consume({
      producerId,
      rtpCapabilities,
      paused: true, // Start paused and have the client resume once ready
    });

    this.consumers.set(consumer.id, consumer);

    consumer.on('producerpause', () => {
      this.notify('producerPaused', { producerId: consumer.producerId });
    });

    consumer.on('producerresume', () => {
      this.notify('producerResumed', { producerId: consumer.producerId });
    });

    consumer.on('transportclose', () => {
      this.consumers.delete(consumer.id);
    });

    consumer.on('transportclose', () => {
      this.consumers.delete(consumer.id);
    });

    return consumer;
  }

  /**
   * Resumes a specific consumer to start receiving media.
   * This is called after the client has created a consumer and is ready to start receiving media.
   */
  public async resumeConsumer(consumerId: string): Promise<void> {
    const consumer = this.consumers.get(consumerId);
    if (!consumer) throw new Error(`Consumer ${consumerId} not found for participant ${this.id}`);

    if (consumer.paused) {
      await consumer.resume();
    }
  }

  /**
   * Pauses a specific producer (e.g., user mutes their mic).
   */
  public async pauseProducer(producerId: string): Promise<void> {
    const producer = this.producers.get(producerId);
    if (!producer) throw new Error(`Producer ${producerId} not found for participant ${this.id}`);

    if (!producer.paused) {
      await producer.pause();
    }
  }

  /**
   * Resumes a specific producer (e.g., user unmutes their mic).
   */
  public async resumeProducer(producerId: string): Promise<void> {
    const producer = this.producers.get(producerId);
    if (!producer) throw new Error(`Producer ${producerId} not found for participant ${this.id}`);

    if (producer.paused) {
      await producer.resume();
    }
  }

  /**
   * Sends a notification message (no response expected) to this participant.
   */
  public notify(method: string, data: any): void {
    this.ws.send(JSON.stringify({ type: 'notification', method, data }));
  }

  /**
   * Closes all resources associated with this participant.
   */
  public close(): void {
    this.transports.forEach((transport) => transport.close());
  }
}