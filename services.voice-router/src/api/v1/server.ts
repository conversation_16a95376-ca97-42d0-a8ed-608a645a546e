import express from 'express';
import http from 'http';
import cors from 'cors';
import { SessionManager } from '../../services/SessionManager';

import createInternalRouter from './internal/routes';
import createVoiceRouter from './voice/routes';

/**
 * Creates and configures the Express HTTP server.
 * @param sessionManager - The singleton instance of the SessionManager.
 * @returns An http.Server instance.
 */
export function createHttpServer(sessionManager: SessionManager): http.Server {
  const app = express();
  const httpServer = http.createServer(app);

  // Apply middleware
  app.use(express.json()); // For parsing application/json

  // Use CORS
  app.use(cors());

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.status(200).send('OK');
  });

  // Create and apply the API routers (versioned)
  app.use('/internal', createInternalRouter(sessionManager));
  app.use('/voice', createVoiceRouter(sessionManager));

  return httpServer;
}

