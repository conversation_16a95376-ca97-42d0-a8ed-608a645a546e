import { Router } from 'express';
import { SessionManager } from '../../../services/SessionManager';
import * as controller from './controller';

export default function createRouter(sessionManager: SessionManager): Router {
  const router = Router();

  // Middleware to inject the SessionManager into each request
  router.use((req, res, next) => {
    req.sessionManager = sessionManager;
    next();
  });

  /**
   * Gets the router RTP capabilities for a given session.
   * GET /v1/voice/sessions/{sessionId}/router-rtp-capabilities
   */
  router.get('/sessions/:sessionId/router-rtp-capabilities', controller.getRouterRtpCapabilities);

  return router;
}

