import { Request, Response } from 'express';
import { SessionIdParamSchema } from './schemas';

/**
 * Gets the router RTP capabilities for a given session.
 */
export async function getRouterRtpCapabilities(req: Request, res: Response): Promise<void> {
  try {
    const { sessionId } = SessionIdParamSchema.parse(req.params);

    const session = req.sessionManager.getSession(sessionId);
    if (!session) {
      res.status(404).json({ error: 'Session not found' });
      return;
    }

    const capabilities = session.getRouterRtpCapabilities();
    res.status(200).json(capabilities);
  } catch (error: any) {
    if (error?.issues) {
      res.status(400).json({ error: 'Invalid request', details: error.issues });
      return;
    }
    console.error('Error getting router RTP capabilities:', error);
    res.status(500).json({ error: 'Failed to get router RTP capabilities' });
  }
}

