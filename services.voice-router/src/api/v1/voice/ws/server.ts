import http from 'http';
import { WebSocketServer, WebSocket } from 'ws';
import type { RawData } from 'ws';
import { z } from 'zod';
import { handleWebSocketMessage } from './handlers';
import { SessionManager } from '../../../../services/SessionManager';
import url from 'url';
import { v4 as uuidV4 } from 'uuid';

/**
 * Creates and attaches the WebSocket server to the main HTTP server.
 * Configured to listen under /v1/voice/ws for clarity.
 */
export function createWebSocketServer(httpServer: http.Server, sessionManager: SessionManager) {
  const wss = new WebSocketServer({ server: httpServer, path: '/voice/ws' });

  const QuerySchema = z.object({ sessionId: z.string().min(1) });

  wss.on('connection', (ws: WebSocket, req: http.IncomingMessage) => {
    const { query } = url.parse(req.url || '', true);
    const parsed = QuerySchema.safeParse(query);

    if (!parsed.success) {
      ws.close(1008, 'Invalid query params');
      return;
    }

    const { sessionId } = parsed.data;
    const session = sessionManager.getSession(sessionId);
    if (!session) {
      ws.close(1011, 'Session not found');
      return;
    }

    const participantId = uuidV4();
    session.addParticipant(participantId, ws);

    ws.on('message', async (raw: RawData) => {
      try {
        const text = typeof raw === 'string' ? raw : raw.toString('utf8');
        const jsonMessage = JSON.parse(text);
        await handleWebSocketMessage(ws, jsonMessage, session, participantId);
      } catch (error) {
        ws.send(JSON.stringify({ type: 'response', id: 0, error: 'Invalid message format' }));
      }
    });

    ws.on('close', () => {
      session.removeParticipant(participantId);
    });
  });
}

