import { WebSocket } from 'ws';
import { z } from 'zod';
import { Session } from '../../../../core/Session';
import { WsRequest } from './schemas';

// A simple helper to structure responses
function sendResponse(ws: WebSocket, requestId: number, data: any) {
  ws.send(JSON.stringify({ type: 'response', id: requestId, data }));
}

function sendError(ws: WebSocket, requestId: number, error: string) {
  ws.send(JSON.stringify({ type: 'response', id: requestId, error }));
}

/**
 * Main handler for all incoming WebSocket messages.
 */
export async function handleWebSocketMessage(
  ws: WebSocket,
  rawMessage: unknown,
  session: Session,
  participantId: string
) {
  try {
    const message = WsRequest.parse(rawMessage);
    const { id, method } = message;

    switch (method) {
      case 'createWebRtcTransport': {
        const transportInfo = await session.createWebRtcTransport({ participantId });
        sendResponse(ws, id, transportInfo);
        break;
      }

      case 'connectWebRtcTransport': {
        await session.connectWebRtcTransport({
          participantId,
          transportId: message.data.transportId,
          dtlsParameters: message.data.dtlsParameters,
        });
        sendResponse(ws, id, { connected: true });
        break;
      }

      case 'produce': {
        const { transportId, kind, rtpParameters, appData } = message.data;
        const producerId = await session.createProducer({
          participantId,
          transportId,
          kind,
          rtpParameters,
          appData: appData ?? {},
        });
        sendResponse(ws, id, { id: producerId });
        break;
      }

      case 'consume': {
        const consumerInfo = await session.createConsumer({ participantId, ...message.data });
        sendResponse(ws, id, consumerInfo);
        break;
      }

      case 'resumeConsumer': {
        await session.resumeConsumer({
          participantId,
          consumerId: message.data.consumerId,
        });
        sendResponse(ws, id, { resumed: true });
        break;
      }

      case 'pauseProducer': {
        await session.pauseProducer({ participantId, producerId: message.data.producerId });
        sendResponse(ws, id, { paused: true });
        break;
      }

      case 'resumeProducer': {
        await session.resumeProducer({ participantId, producerId: message.data.producerId });
        sendResponse(ws, id, { resumed: true });
        break;
      }

      default: {
        sendError(ws, id, `Unknown method: ${method}`);
        break;
      }
    }
  } catch (error: any) {
    if (error instanceof z.ZodError) {
      const issues = error.issues.map((i: z.ZodIssue) => `${i.path.join('.')}: ${i.message}`).join('; ');
      const id = (typeof (rawMessage as any)?.id === 'number' ? (rawMessage as any).id : 0) as number;
      sendError(ws, id, `Invalid request: ${issues}`);
      return;
    }
    const id = (typeof (rawMessage as any)?.id === 'number' ? (rawMessage as any).id : 0) as number;
    sendError(ws, id, error?.message || 'An internal server error occurred');
  }
}

