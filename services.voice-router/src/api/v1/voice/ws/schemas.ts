import { z } from 'zod';

// Common primitives
const Id = z.number().int().nonnegative();
const StrId = z.string().min(1);

// WebSocket request schemas (discriminated by method)
export const CreateWebRtcTransportReq = z.object({
  id: Id,
  method: z.literal('createWebRtcTransport'),
  data: z.unknown().optional(),
});

export const ConnectWebRtcTransportReq = z.object({
  id: Id,
  method: z.literal('connectWebRtcTransport'),
  data: z.object({
    transportId: StrId,
    dtlsParameters: z.any(), // mediasoup DtlsParameters; keep as any to avoid coupling
  }),
});

export const ProduceReq = z.object({
  id: Id,
  method: z.literal('produce'),
  data: z.object({
    transportId: StrId,
    kind: z.union([z.literal('audio'), z.literal('video')]),
    rtpParameters: z.any(),
    appData: z
      .object({
        role: z.enum(['caller', 'operator']).optional(),
      })
      .optional(),
  }),
});

export const ConsumeReq = z.object({
  id: Id,
  method: z.literal('consume'),
  data: z.object({
    transportId: StrId,
    producerId: StrId,
    rtpCapabilities: z.any(),
  }),
});

export const ResumeConsumerReq = z.object({
  id: Id,
  method: z.literal('resumeConsumer'),
  data: z.object({ consumerId: StrId }),
});

export const PauseProducerReq = z.object({
  id: Id,
  method: z.literal('pauseProducer'),
  data: z.object({ producerId: StrId }),
});

export const ResumeProducerReq = z.object({
  id: Id,
  method: z.literal('resumeProducer'),
  data: z.object({ producerId: StrId }),
});

export const WsRequest = z.discriminatedUnion('method', [
  CreateWebRtcTransportReq,
  ConnectWebRtcTransportReq,
  ProduceReq,
  ConsumeReq,
  ResumeConsumerReq,
  PauseProducerReq,
  ResumeProducerReq,
]);

export type WsRequest = z.infer<typeof WsRequest>;

// Outgoing messages
export const WsResponse = z.object({
  type: z.literal('response'),
  id: Id,
  data: z.unknown().optional(),
  error: z.string().optional(),
});

export const WsNotification = z.object({
  type: z.literal('notification'),
  method: z.string(),
  data: z.unknown().optional(),
});

export type WsResponse = z.infer<typeof WsResponse>;
export type WsNotification = z.infer<typeof WsNotification>;

