import { Request, Response } from 'express';
import { SessionIdParamSchema, AttachBodySchema } from './schemas';

/**
 * Handles the creation of a new session.
 * Responds with the unique ID of the session.
 */
export async function createSession(req: Request, res: Response): Promise<void> {
  try {
    const sessionId = await req.sessionManager.createSession();
    res.status(201).json({ mediaSessionId: sessionId });
  } catch (error) {
    console.error('Error creating call:', error);
    res.status(500).json({ error: 'Failed to create media session' });
  }
}

/**
 * Handles the deletion of a session.
 */
export function deleteSession(req: Request, res: Response): void {
  try {
    const { sessionId } = SessionIdParamSchema.parse(req.params);
    req.sessionManager.deleteSession(sessionId);
    res.status(204).send();
  } catch (error: any) {
    if (error?.issues) {
      res.status(400).json({ error: 'Invalid request', details: error.issues });
      return;
    }
    console.error('Error deleting call:', error);
    res.status(500).json({ error: 'Failed to delete media session' });
  }
}

/**
 * Forks audio from a session to the AI service.
 * POST /v1/internal/sessions/:sessionId/attach
 */
export async function attach(req: Request, res: Response): Promise<void> {
  try {
    const { sessionId } = SessionIdParamSchema.parse(req.params);
    const { targets } = AttachBodySchema.parse(req.body);

    const session = req.sessionManager.getSession(sessionId);
    if (!session) {
      res.status(404).json({ error: 'Session not found' });
      return;
    }

    const ssrcMapping = await session.startForking(targets);

    res.status(200).json({ status: 'forking_started', ssrcMapping });
  } catch (error: any) {
    if (error?.issues) {
      res.status(400).json({ error: 'Invalid request', details: error.issues });
      return;
    }
    console.error('Error in attach:', error);
    res.status(500).json({ error: 'Failed to attach' });
  }
}

