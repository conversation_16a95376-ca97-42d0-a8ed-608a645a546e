import { Router } from 'express';
import { SessionManager } from '../../../services/SessionManager';
import * as controller from './controller';

export default function createRouter(sessionManager: SessionManager): Router {
  const router = Router();

  // Middleware to inject the SessionManager into each request
  router.use((req, res, next) => {
    req.sessionManager = sessionManager;
    next();
  });

  /**
   * Creates a new media session.
   * POST /v1/internal/sessions
   */
  router.post('/sessions', controller.createSession);

  /**
   * Terminates an entire media session.
   * DELETE /v1/internal/sessions/{sessionId}
   */
  router.delete('/sessions/:sessionId', controller.deleteSession);

  /**
   * Attach/fork endpoint for AI service
   * POST /v1/internal/sessions/{sessionId}/attach
   */
  router.post('/sessions/:sessionId/attach', controller.attach);

  return router;
}

