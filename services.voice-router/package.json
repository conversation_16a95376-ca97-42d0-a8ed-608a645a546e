{"name": "cortexa-voice-router", "version": "1.0.0", "description": "SFU for WebRTC voice-router service", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "watch": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@types/cors": "^2.8.19", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^4.18.2", "kafkajs": "^2.2.4", "mediasoup": "^3.12.9", "socket.io": "^4.7.2", "uuid": "^11.1.0", "ws": "^8.18.3", "zod": "^4.1.5"}, "devDependencies": {"@types/express": "^4.17.17", "@types/jest": "^30.0.0", "@types/node": "^20.8.0", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "jest": "^29.7.0", "ts-jest": "^29.4.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}, "engines": {"node": ">=18.0.0"}, "author": "", "license": "ISC"}