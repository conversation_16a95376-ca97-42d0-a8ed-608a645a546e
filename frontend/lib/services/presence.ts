"use client"

import { BaseWebSocketService, defaultSupabase, apiBaseUrlFromEnv } from './base'

export const PARTICIPANT_STATUSES = ['available', 'busy', 'offline'] as const;
export type ParticipantStatus = (typeof PARTICIPANT_STATUSES)[number];

export type Participant = {
  id: string
  role: string
  status: ParticipantStatus
}

interface BackendParticipantUpdate {
  participant_id: string
  status: ParticipantStatus
}


/**
 * Manages the presence for an Operator. This class connects to the operator 
 * endpoint, sends status updates, and automatically tracks user activity.
 */
export class OperatorPresenceService extends BaseWebSocketService {
  #currentStatus: ParticipantStatus = 'offline'
  #idleTimer: ReturnType<typeof setTimeout> | null = null
  #activityBound = false
  #visHandler: ((this: Document, ev: DocumentEventMap['visibilitychange']) => unknown) | null = null
  static IDLE_TIMEOUT_MS = 60_000

  get currentStatus(): ParticipantStatus { return this.#currentStatus }

  connect(): Promise<void> {
    return this._connectTo('/presence/ws');
  }

  updateStatus(status: ParticipantStatus): void {
    if (this._ws?.readyState !== WebSocket.OPEN) {
      console.warn("Cannot update status: WebSocket is not connected.");
      return;
    }
    this._ws.send(JSON.stringify({ status }));
  }

  #emitStatus(status: ParticipantStatus) {
    if (this.#currentStatus !== status) {
      this.#currentStatus = status
      this.emit('status-change', status)
    }
  }

  #setStatus(status: ParticipantStatus) {
    this.#emitStatus(status)
    this.updateStatus(status)
  }

  #resetIdleTimer() {
    if (this.#idleTimer) clearTimeout(this.#idleTimer)
    // Temporarily disabled: do not auto-set to 'busy' based on inactivity
    this.#idleTimer = null
  }

  #onUserActivity = () => {
    if (this._ws?.readyState === WebSocket.OPEN) {
      if (this.#currentStatus !== 'available') {
        this.#setStatus('available')
      }
      // this.#resetIdleTimer() // busy-on-inactivity disabled
    }
  }

  #bindActivityListeners() {
    if (this.#activityBound) return;
    const events: Array<keyof DocumentEventMap> = ['mousemove', 'keydown', 'click', 'scroll', 'touchstart'];
    events.forEach(evt => window.addEventListener(evt, this.#onUserActivity, { passive: true }));
    this.#visHandler = () => this.#onUserActivity(); // busy-on-inactivity disabled
    document.addEventListener('visibilitychange', this.#visHandler);
    this.#activityBound = true;
  }

  #unbindActivityListeners() {
    if (!this.#activityBound) return;
    const events: Array<keyof DocumentEventMap> = ['mousemove', 'keydown', 'click', 'scroll', 'touchstart'];
    events.forEach(evt => window.removeEventListener(evt, this.#onUserActivity));
    if (this.#visHandler) document.removeEventListener('visibilitychange', this.#visHandler);
    if (this.#idleTimer) clearTimeout(this.#idleTimer);
    this.#idleTimer = null;
    this.#activityBound = false;
  }

  protected _setupWsEvents(): void {
    if (!this._ws) return;
    this._ws.onopen = () => {
      console.log("Presence WebSocket connected as OPERATOR.");
      this.emit('connect');
      this.#setStatus('available');
      //this.#bindActivityListeners(); This is annoying for testing - disabling
      //this.#resetIdleTimer(); // busy-on-inactivity disabled
    };
    this._ws.onclose = (event) => {
      console.log(`Operator WebSocket disconnected: ${event.reason}`);
      //this.#unbindActivityListeners(); This is annoying for testing - disabling
      this.#emitStatus('offline');
      this.emit('disconnect', { code: event.code, reason: event.reason });
      this._ws = null;
    };
    this._ws.onerror = (error) => this.emit('error', error);
  }
}

/**
 * Manages presence for a Manager. This class connects to a read-only
 * endpoint to receive real-time status updates for all participants.
 */
export class ManagerPresenceService extends BaseWebSocketService {
  #participants = new Map<string, Participant>()

  get participantList(): Participant[] {
    return Array.from(this.#participants.values());
  }

  connect(): Promise<void> {
    return this._connectTo('/presence/ws/manager');
  }

  protected _setupWsEvents(): void {
    if (!this._ws) return;
    this._ws.onopen = () => {
      console.log("Presence WebSocket connected as MANAGER.");
      this.emit('connect');
    };
    this._ws.onclose = (event) => {
      console.log(`Manager WebSocket disconnected: ${event.reason}`);
      this.#participants.clear();
      this.emit('participants-update', []);
      this.emit('disconnect', { code: event.code, reason: event.reason });
      this._ws = null;
    };
    this._ws.onerror = (error) => this.emit('error', error);
    this._ws.onmessage = (event) => {
      try {
        const data: BackendParticipantUpdate = JSON.parse(event.data);
        const existing = this.#participants.get(data.participant_id);

        this.#participants.set(data.participant_id, {
          id: data.participant_id,
          status: data.status,
          role: existing?.role || 'unknown',
        });

        this.emit('participants-update', this.participantList);
      } catch (e) {
        console.error("Failed to parse manager message:", event.data, e);
      }
    };
  }
}

// --- Singleton Instances ---

const supabase = defaultSupabase
const apiBaseUrl = apiBaseUrlFromEnv()

export const operatorPresenceService = new OperatorPresenceService(supabase, apiBaseUrl)
export const managerPresenceService = new ManagerPresenceService(supabase, apiBaseUrl)