"use client"

import type { SupabaseClient } from '@supabase/supabase-js'
import { createClient as createBrowserSupabase } from '@/lib/supabase/client'

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type EventListener = (...args: any[]) => void;

export class EventEmitter {
  private listeners: Record<string, EventListener[]> = {};

  on(event: string, listener: EventListener) {
    if (!this.listeners[event]) this.listeners[event] = [];
    this.listeners[event].push(listener);
  }

  off(event: string, listener: EventListener) {
    if (!this.listeners[event]) return;
    this.listeners[event] = this.listeners[event].filter(l => l !== listener);
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  emit(event: string, ...args: any[]) {
    if (!this.listeners[event]) return;
    this.listeners[event].forEach(listener => listener(...args));
  }
}

export abstract class BaseService extends EventEmitter {
  protected _supabase: SupabaseClient
  protected _apiBaseUrl: string

  constructor(supabaseClient: SupabaseClient, apiBaseUrl: string) {
    super();
    this._supabase = supabaseClient
    this._apiBaseUrl = apiBaseUrl
  }

  protected async getAccessToken(): Promise<string> {
    const { data, error } = await this._supabase.auth.getSession()
    if (error || !data.session?.access_token) {
      throw new Error('Not authenticated or missing access token')
    }
    return data.session.access_token
  }

  protected getWsUrl(endpoint: string): string {
    return `${this._apiBaseUrl.replace(/^http/, 'ws')}${endpoint}`
  }
}

export abstract class BaseWebSocketService extends BaseService {
  protected _ws: WebSocket | null = null

  protected abstract _setupWsEvents(): void

  protected async _connectTo(endpoint: string): Promise<void> {
    if (this._ws?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected.')
      return
    }
    try {
      const token = await this.getAccessToken()
      const wsUrl = `${this.getWsUrl(endpoint)}?token=${token}`
      this._ws = new WebSocket(wsUrl)
      this._setupWsEvents()
    } catch (error) {
      console.error('Failed to establish websocket connection:', error)
      this.emit('error', error)
    }
  }

  disconnect(): void {
    if (this._ws) {
      this._ws.close(1000, 'User initiated disconnect')
    }
  }
}

export class BaseHttpClient extends BaseService {
  protected async authorizedFetch(input: RequestInfo | URL, init: RequestInit = {}): Promise<Response> {
    const token = await this.getAccessToken()
    const headers = new Headers(init.headers || {})
    headers.set('Authorization', `Bearer ${token}`)

    // Set default Content-Type for POST, PUT, etc. if not already set
    if (!headers.has('Content-Type') && init.method && init.method !== 'GET') {
      headers.set('Content-Type', 'application/json')
    }

    const response = await fetch(input, { ...init, headers, credentials: 'include' })

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Could not read error response.')
      const method = init.method || 'GET'
      throw new Error(`API Error: ${method} ${input} failed with status ${response.status}: ${errorText}`)
    }

    return response
  }
}

export const defaultSupabase = createBrowserSupabase()
export function apiBaseUrlFromEnv(): string {
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL
  if (!apiBaseUrl) {
    throw new Error('Missing NEXT_PUBLIC_API_BASE_URL environment variable.')
  }
  return apiBaseUrl
}

