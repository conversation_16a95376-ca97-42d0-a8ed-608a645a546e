"use client"

import { BaseWebSocketService, defaultSupabase, apiBaseUrlFromEnv, EventListener } from './base'


export type CallAssignedPayload = {
  source_service: string
  event_type: string
  call_id: string
  operator_id: string
}


export type WebrtcConnectionDetailsPayload = {
  source_service: string
  event_type: string
  call_id: string
  operator_id: string
  media_session_id: string
}


export type TranscriptionCompletedPayload = {
  source_service: string
  event_type: string
  call_id: string
  participant_id: string
  segment_id: string
  text: string
  confidence: number
}



export class WebsocketBridgeService extends BaseWebSocketService {
  async connect(): Promise<void> {
    return this._connectTo('/bridge/ws')
  }

  protected _setupWsEvents(): void {
    if (!this._ws) return

    this._ws.onopen = () => {
      console.log('Bridge WebSocket connected.')
      this.emit('connect')
    }

    this._ws.onclose = (event) => {
      console.log(`Bridge WebSocket disconnected: ${event.reason}`)
      this.emit('disconnect', { code: event.code, reason: event.reason })
      this._ws = null
    }

    this._ws.onerror = (error) => this.emit('error', error)

    this._ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data) as Record<string, unknown>
        const eventType = (data['event_type'] as string) || (data['type'] as string) || 'message'
        this.emit(eventType, data)
      } catch (e) {
        console.error('Failed to parse bridge message:', event.data, e)
      }
    }
  }

  // Convenience listener for call.assigned
  onCallAssigned(listener: (payload: CallAssignedPayload) => void) {
    this.on('call.assigned', listener as unknown as EventListener)
  }

  onWebrtcConnectionDetails(listener: (payload: WebrtcConnectionDetailsPayload) => void) {
    this.on('webrtc.connection-details', listener as unknown as EventListener)
  }

  onTranscriptionCompleted(listener: (payload: TranscriptionCompletedPayload) => void) {
    this.on('transcription.completed', listener as unknown as EventListener)
  }
}

// --- Singleton instance ---
const supabase = defaultSupabase
const apiBaseUrl = apiBaseUrlFromEnv()

export const wsBridge = new WebsocketBridgeService(supabase, apiBaseUrl)
