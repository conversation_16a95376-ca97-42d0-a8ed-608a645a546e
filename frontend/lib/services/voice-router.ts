"use client"

import { Device } from 'mediasoup-client'

export type VoiceRouterSession = {
  close: () => void
}

export type ConnectOptions = {
  mediaSessionId: string
  httpBaseUrl: string
  remoteAudioEl?: HTMLAudioElement | null
}

export async function connectToVoiceRouter(opts: ConnectOptions): Promise<VoiceRouterSession> {
  const { mediaSessionId, httpBaseUrl, remoteAudioEl } = opts

  const http = httpBaseUrl.replace(/\/$/, '')
  const wsUrl = `${httpBaseUrl.replace(/\/$/, '')}/voice/ws?sessionId=${encodeURIComponent(mediaSessionId)}`

  // Step A: Fetch router RTP capabilities
  const capsRes = await fetch(`${http}/voice/sessions/${encodeURIComponent(mediaSessionId)}/router-rtp-capabilities`)
  if (!capsRes.ok) throw new Error(`Failed to fetch router RTP capabilities: ${capsRes.statusText}`)
  const routerRtpCapabilities = await capsRes.json()

  // Step B: Load mediasoup device
  const device = new Device()
  await device.load({ routerRtpCapabilities })

  // Step C: Connect WebSocket signaling
  const ws = new WebSocket(wsUrl)
  let nextRequestId = 1
  const pending = new Map<number, { resolve: (data: any) => void, reject: (err: any) => void }>()

  function sendRequest(method: string, data: Record<string, any> = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      if (ws.readyState !== WebSocket.OPEN) return reject(new Error('Signaling WebSocket not open'))
      const id = nextRequestId++
      pending.set(id, { resolve, reject })
      ws.send(JSON.stringify({ type: 'request', id, method, data }))
    })
  }

  function handleNotification(msg: any) {
    if (msg.method === 'newProducer' && msg.data?.producerId) {
      void consumeNewProducer(msg.data.producerId)
    }
  }

  ws.onmessage = (event) => {
    try {
      const msg = JSON.parse(event.data)
      if (msg.type === 'response') {
        const p = pending.get(msg.id)
        if (p) {
          pending.delete(msg.id)
          if (msg.error) p.reject(new Error(msg.error))
          else p.resolve(msg.data)
        }
      } else if (msg.type === 'notification') {
        handleNotification(msg)
      }
    } catch (e) {
      // ignore parse errors
    }
  }

  await new Promise<void>((resolve, reject) => {
    const onOpen = () => { cleanup(); resolve() }
    const onErr = (e: Event) => { cleanup(); reject(e) }
    const cleanup = () => {
      ws.removeEventListener('open', onOpen)
      ws.removeEventListener('error', onErr)
    }
    if (ws.readyState === WebSocket.OPEN) resolve()
    else {
      ws.addEventListener('open', onOpen, { once: true })
      ws.addEventListener('error', onErr, { once: true })
    }
  })

  // Step D: Create send transport and start microphone
  const sendTransportParams = await sendRequest('createWebRtcTransport')
  const sendTransport = device.createSendTransport(sendTransportParams)

  sendTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
    try {
      await sendRequest('connectWebRtcTransport', { transportId: sendTransport.id, dtlsParameters })
      callback()
    } catch (e) { errback(e as Error) }
  })

  sendTransport.on('produce', async ({ kind, rtpParameters, appData }, callback, errback) => {
    try {
      const { id } = await sendRequest('produce', { transportId: sendTransport.id, kind, rtpParameters, appData })
      callback({ id })
    } catch (e) { errback(e as Error) }
  })

  const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
  const audioTrack = stream.getAudioTracks()[0]
  await sendTransport.produce({ track: audioTrack, appData: { mediaSessionId, role: 'operator' } })

  // Step E: Create recv transport and handle consuming
  const recvTransportParams = await sendRequest('createWebRtcTransport')
  const recvTransport = device.createRecvTransport(recvTransportParams)

  recvTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
    try {
      await sendRequest('connectWebRtcTransport', { transportId: recvTransport.id, dtlsParameters })
      callback()
    } catch (e) { errback(e as Error) }
  })

  async function consumeNewProducer(producerId: string) {
    try {
      const consumerParams = await sendRequest('consume', {
        transportId: recvTransport.id,
        producerId,
        rtpCapabilities: device.rtpCapabilities,
      })

      const consumer = await recvTransport.consume(consumerParams)
      const track = consumer.track

      if (remoteAudioEl) {
        const media = remoteAudioEl.srcObject instanceof MediaStream ? remoteAudioEl.srcObject as MediaStream : new MediaStream()
        media.addTrack(track)
        remoteAudioEl.srcObject = media
        remoteAudioEl.play().catch(() => {/* autoplay might be blocked */})
      }

      // Request server to resume (if paused by default)
      await sendRequest('resumeConsumer', { consumerId: consumer.id })
    } catch (e) {
      // eslint-disable-next-line no-console
      console.error('Failed to consume producer', e)
    }
  }

  function close() {
    try { ws.close() } catch {}
    try { sendTransport.close() } catch {}
    try { recvTransport.close() } catch {}
    try { audioTrack.stop() } catch {}
  }

  return { close }
}

