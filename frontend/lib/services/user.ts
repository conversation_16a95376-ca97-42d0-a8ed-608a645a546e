"use client"

import { BaseHttpClient, defaultSupabase, apiBaseUrlFromEnv } from './base'
import type { SupabaseClient } from '@supabase/supabase-js'

export const ROLES = ['operator', 'translator', 'administrator', 'manager'] as const
export type Role = (typeof ROLES)[number]

export type UserCreate = {
  email: string
  password: string
  role: Role
}

export type UserResponse = {
  id: string
  role: string | null
  email: string
  created_at: string
}

export class UserApiClient extends BaseHttpClient {
  #baseUrl: string

  /**
   * Initializes the User API Client.
   * @param supabaseClient - An instance of the Supabase client.
   * @param apiBaseUrl - The base URL for the API endpoint.
   */
  constructor(supabaseClient: SupabaseClient, apiBaseUrl: string) {
    super(supabaseClient, apiBaseUrl)
    this.#baseUrl = `${apiBaseUrl}/users`
  }

  // Auth and authorizedFetch provided by BaseHttpClient

  /**
   * Fetches a list of all users.
   * Corresponds to: GET /users
   * @returns A promise that resolves with an array of user objects.
   */
  async listUsers(): Promise<UserResponse[]> {
    const res = await this.authorizedFetch(this.#baseUrl, { method: 'GET' })
    return res.json()
  }

  /**
   * Creates a new user.
   * Corresponds to: POST /users
   * @param payload - The data for the new user.
   * @returns A promise that resolves with the created user object.
   */
  async createUser(payload: UserCreate): Promise<UserResponse> {
    const res = await this.authorizedFetch(this.#baseUrl, {
      method: 'POST',
      body: JSON.stringify(payload),
    })
    return res.json()
  }

  /**
   * Assigns a role to a specific user.
   * Corresponds to: PUT /users/{user_id}/roles/{role_name}
   * @param userId - The ID of the user.
   * @param roleName - The role to assign.
   * @returns A promise that resolves when the operation is complete.
   */
  async assignRole(userId: string, roleName: Role): Promise<void> {
    const url = `${this.#baseUrl}/${encodeURIComponent(userId)}/roles/${encodeURIComponent(roleName)}`
    await this.authorizedFetch(url, { method: 'PUT' })
  }

  /**
   * Deletes a specific user.
   * Corresponds to: DELETE /users/{user_id}
   * @param userId - The ID of the user to delete.
   * @returns A promise that resolves when the operation is complete.
   */
  async deleteUser(userId: string): Promise<void> {
    const url = `${this.#baseUrl}/${encodeURIComponent(userId)}`
    await this.authorizedFetch(url, { method: 'DELETE' })
  }
}

// --- Singleton Instance ---
const supabase = defaultSupabase
const apiBaseUrl = apiBaseUrlFromEnv()

export const userManagementApi = new UserApiClient(supabase, apiBaseUrl);