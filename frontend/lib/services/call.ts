"use client"

import { BaseHttpClient, defaultSupabase, apiBaseUrlFromEnv } from './base'
import type { SupabaseClient } from '@supabase/supabase-js'

export class CallApiClient extends BaseHttpClient {
  #baseUrl: string

  constructor(supabaseClient: SupabaseClient, apiBaseUrl: string) {
    super(supabaseClient, apiBaseUrl)
    this.#baseUrl = `${apiBaseUrl}/calls`
  }

  async acceptCall(callId: string): Promise<void> {
    const url = `${this.#baseUrl}/${encodeURIComponent(callId)}/accept`
    await this.authorizedFetch(url, { method: 'POST' })
  }
}

// --- Singleton Instance ---
const supabase = defaultSupabase
const apiBaseUrl = apiBaseUrlFromEnv()

export const callApi = new CallApiClient(supabase, apiBaseUrl)
