'use client'

import { FormEvent, useEffect, useMemo, useState } from 'react'
import { userManagementApi, ROL<PERSON>, Role, UserResponse } from '@/lib/services/user'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

export function UserManagementPanel() {
  // State for the list of users and loading/error status
  const [users, setUsers] = useState<UserResponse[]>([])
  const [loading, setLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)

  // State for the "Create User" form
  const [email, setEmail] = useState<string>('')
  const [password, setPassword] = useState<string>('')
  const [role, setRole] = useState<Role>(ROLES[0]) // Default to the first role
  const [creating, setCreating] = useState<boolean>(false)

  /**
   * Fetches the list of users from the API and updates the component's state.
   */
  const fetchUsers = async () => {
    try {
      setLoading(true)
      setError(null)
      const userList = await userManagementApi.listUsers()
      setUsers(userList)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  // Fetch users when the component mounts
  useEffect(() => {
    fetchUsers()
  }, [])

  /**
   * Memoized sorted list of users to prevent re-sorting on every render.
   * Users are sorted by creation date in descending order (newest first).
   */
  const sortedUsers = useMemo(() => {
    return [...users].sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
  }, [users])

  /**
   * Handles the form submission for creating a new user.
   */
  const handleCreateUser = async (e: FormEvent) => {
    e.preventDefault()
    if (!email || !password) return

    setCreating(true)
    setError(null)
    try {
      await userManagementApi.createUser({ email, password, role })
      // Reset form and refresh user list
      setEmail('')
      setPassword('')
      setRole(ROLES[0])
      await fetchUsers()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create user')
    } finally {
      setCreating(false)
    }
  }

  /**
   * Handles assigning a new role to a user.
   */
  const handleAssignRole = async (userId: string, newRole: Role) => {
    // Optimistically update the UI
    setUsers(users.map(u => u.id === userId ? { ...u, role: newRole } : u))

    try {
      await userManagementApi.assignRole(userId, newRole)
      // Optionally, you could re-fetch here to confirm, but optimistic is faster
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to assign role')
      // Revert the change on error
      fetchUsers()
    }
  }

  /**
   * Handles the deletion of a user.
   */
  const handleDeleteUser = async (userId: string) => {
    if (!window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        return;
    }
    
    // Optimistically remove from UI
    setUsers(users.filter((u) => u.id !== userId))

    try {
      await userManagementApi.deleteUser(userId)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete user')
      // Revert the change on error by re-fetching
      fetchUsers()
    }
  }

  return (
    <div className="mx-auto max-w-5xl w-full space-y-6 p-4">
      <Card>
        <CardHeader>
          <CardTitle>User Management</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">Loading users...</div>
          ) : error ? (
            <div className="text-red-600 bg-red-50 p-4 rounded-md">{error}</div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="text-left border-b">
                    <th className="py-2 pr-4 font-semibold">Email</th>
                    <th className="py-2 pr-4 font-semibold">Role</th>
                    <th className="py-2 pr-4 font-semibold">Created</th>
                    <th className="py-2 font-semibold text-right">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {sortedUsers.map((u) => (
                    <tr key={u.id} className="border-b hover:bg-gray-50">
                      <td className="py-2 pr-4 font-medium">{u.email}</td>
                      <td className="py-2 pr-4">
                        <select
                          className="border rounded px-2 py-1 bg-white"
                          value={u.role ?? ''}
                          onChange={(e) => handleAssignRole(u.id, e.target.value as Role)}
                        >
                          <option value="">(none)</option>
                          {ROLES.map((r) => (
                            <option key={r} value={r}>
                              {r.charAt(0).toUpperCase() + r.slice(1)}
                            </option>
                          ))}
                        </select>
                      </td>
                      <td className="py-2 pr-4 text-gray-600">
                        {new Date(u.created_at).toLocaleString()}
                      </td>
                      <td className="py-2">
                        <div className="flex gap-2 justify-end">
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => handleDeleteUser(u.id)}
                            title="Delete user"
                          >
                            Delete
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Create New User</CardTitle>
        </CardHeader>
        <CardContent>
          <form className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end" onSubmit={handleCreateUser}>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input id="email" value={email} onChange={(e) => setEmail(e.target.value)} required type="email" placeholder="<EMAIL>" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input id="password" value={password} onChange={(e) => setPassword(e.target.value)} required type="password" placeholder="••••••••" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="role">Role</Label>
              <select
                id="role"
                className="w-full border rounded px-2 py-2 h-10 bg-white"
                value={role}
                onChange={(e) => setRole(e.target.value as Role)}
              >
                {ROLES.map((r) => (
                  <option key={r} value={r}>
                    {r.charAt(0).toUpperCase() + r.slice(1)}
                  </option>
                ))}
              </select>
            </div>
            <div className="md:pt-2">
              <Button type="submit" disabled={creating} className="w-full">
                {creating ? 'Creating...' : 'Create User'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

