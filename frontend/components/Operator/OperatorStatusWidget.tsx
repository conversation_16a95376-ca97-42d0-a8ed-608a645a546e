"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { LogOut } from "lucide-react"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { operatorPresenceService } from "@/lib/services/presence";
import type { ParticipantStatus } from "@/lib/services/presence";

import { cn } from "@/lib/utils"

/**
 * A map to associate each status with a display label and color.
 */
const statusConfig: Record<ParticipantStatus, { label: string; color: string }> = {
  available: { label: "Available", color: "bg-green-500" },
  busy: { label: "Busy", color: "bg-yellow-500" },
  offline: { label: "Offline", color: "bg-slate-400" },
}

/**
 * A dashboard widget that shows the operator's current presence status.
 * It automatically updates based on events from the presence service
 * and provides an option to go offline.
 */
export function OperatorStatusWidget() {
  const router = useRouter()
  
  const [status, setStatus] = useState<ParticipantStatus>(
    operatorPresenceService.currentStatus
  )

  useEffect(() => {
    const handleStatusChange = (newStatus: ParticipantStatus) => {
      setStatus(newStatus)
    }

    operatorPresenceService.on("status-change", handleStatusChange)
    operatorPresenceService.connect()

    return () => {
      operatorPresenceService.off("status-change", handleStatusChange)
      operatorPresenceService.disconnect()
    }
  }, [])

  const handleGoOffline = () => {
    operatorPresenceService.disconnect()
    router.push("/") // Redirect to the homepage
  }

  const currentConfig = statusConfig[status]

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className="flex min-w-[120px] items-center justify-start gap-2 transition-colors"
          aria-label={`Current status: ${currentConfig.label}`}
        >
          {/* Status Indicator Dot */}
          <span className={cn("h-3 w-3 rounded-full", currentConfig.color)} />
          
          {/* Status Text */}
          <span className="flex-1 text-left">{currentConfig.label}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[180px]">
        <DropdownMenuItem onSelect={handleGoOffline} className="cursor-pointer text-red-500 focus:text-red-500">
          <LogOut className="mr-2 h-4 w-4" />
          <span>Go Offline</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}