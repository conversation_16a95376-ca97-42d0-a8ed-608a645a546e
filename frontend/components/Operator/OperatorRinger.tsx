"use client"

import { useEffect, useRef, useState } from 'react'
import { wsBridge } from '@/lib/services/wsbridge'
import { callApi } from '@/lib/services/call'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'

export function OperatorRinger() {
  const [ringing, setRinging] = useState(false)
  const [callId, setCallId] = useState<string | null>(null)
  const [audioBlocked, setAudioBlocked] = useState(false)
  const [accepting, setAccepting] = useState(false)

  const audioRef = useRef<HTMLAudioElement | null>(null)

  useEffect(() => {
    const handleAssigned = (payload: { call_id: string }) => {
      setCallId(payload.call_id)
      startRinging()
    }

    wsBridge.onCallAssigned(handleAssigned as never)
    wsBridge.connect()

    return () => {
      wsBridge.off('call.assigned', handleAssigned as never)
      wsBridge.disconnect()
      stopRinging()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  async function startRinging() {
    if (ringing) return
    setRinging(true)

    if (!audioRef.current) {
      audioRef.current = new Audio('/ring.mp3')
      audioRef.current.loop = true
    }
    try {
      await audioRef.current.play()
      setAudioBlocked(false)
    } catch {
      // Autoplay likely blocked until user interaction
      setAudioBlocked(true)
    }
  }

  function stopRinging() {
    setRinging(false)
    setAudioBlocked(false)
    if (audioRef.current) {
      try { audioRef.current.pause() } catch {}
      audioRef.current.currentTime = 0
    }
  }

  async function acceptCall() {
    if (!callId || accepting) return
    try {
      setAccepting(true)
      await callApi.acceptCall(callId)
      stopRinging()
    } catch (err) {
      console.error('Failed to accept call', err)
    } finally {
      setAccepting(false)
    }
  }

  return (
    <>
      {ringing && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
          <Card className="w-full max-w-sm p-6 shadow-xl">
            <div className="space-y-2">
              <h2 className="text-lg font-semibold">Incoming call</h2>
              {callId && <p className="text-sm text-muted-foreground">Call ID: {callId}</p>}
              {audioBlocked && (
                <p className="text-xs text-amber-600">Sound is blocked by the browser until you interact.</p>
              )}
              <div className="pt-4 flex gap-2 justify-end">
                {audioBlocked && (
                  <Button variant="secondary" onClick={() => audioRef.current?.play().then(() => setAudioBlocked(false)).catch(() => setAudioBlocked(true))}>
                    Play sound
                  </Button>
                )}
                <Button onClick={acceptCall} disabled={!callId || accepting}>
                  {accepting ? 'Accepting…' : 'Accept'}
                </Button>
                <Button variant="outline" onClick={stopRinging} disabled={accepting}>Dismiss</Button>
              </div>
            </div>
          </Card>
        </div>
      )}
    </>
  )
}

