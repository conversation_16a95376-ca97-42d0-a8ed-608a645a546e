"use client"

import { useState, useEffect, useMemo } from "react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { managerPresenceService, type Participant, type ParticipantStatus } from "@/lib/services/presence"

/**
 * A map to associate each status with a display style for the Badge component.
 */
const statusStyles: Record<
  ParticipantStatus,
  { label: string; variant: "default" | "secondary" | "destructive" | "outline" }
> = {
  available: { label: "Available", variant: "default" },
  busy: { label: "Busy", variant: "secondary" },
  offline: { label: "Offline", variant: "outline" },
}

/**
 * A dashboard panel for Managers to view the real-time status of all Operators.
 * It connects to the manager presence service and displays a live-updating list.
 */
export function ParticipantPresencePanel() {
  const [participants, setParticipants] = useState<Participant[]>(
    managerPresenceService.participantList
  )

  useEffect(() => {
    const handleUpdate = (updatedList: Participant[]) => {
      // Create a new array to ensure React detects the state change
      setParticipants([...updatedList])
    }

    managerPresenceService.on("participants-update", handleUpdate)
    managerPresenceService.connect()

    return () => {
      managerPresenceService.off("participants-update", handleUpdate)
      managerPresenceService.disconnect()
    }
  }, [])

  const sortedParticipants = useMemo(() => {
    return [...participants].sort((a, b) => a.id.localeCompare(b.id))
  }, [participants])

  return (
    <Card>
      <CardHeader>
        <CardTitle>Operator Status</CardTitle>
        <CardDescription>
          A live overview of your team&apos;s current status.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Operator ID</TableHead>
              <TableHead className="w-[120px] text-right">Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedParticipants.length > 0 ? (
              sortedParticipants.map((participant) => (
                <TableRow key={participant.id}>
                  <TableCell className="font-medium">
                    <span title={participant.id}>
                      {participant.id.substring(0, 8)}...
                    </span>
                  </TableCell>
                  <TableCell className="text-right">
                    <Badge variant={statusStyles[participant.status].variant}>
                      {statusStyles[participant.status].label}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={2}
                  className="h-24 text-center text-muted-foreground"
                >
                  No operators currently online.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}