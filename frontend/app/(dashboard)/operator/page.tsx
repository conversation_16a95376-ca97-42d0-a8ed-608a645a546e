import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { OperatorRinger } from '@/components/Operator/OperatorRinger'
import { OperatorCall } from '@/components/Operator/OperatorCall'

export default async function OperatorPage() {
  const supabase = await createClient()

  const { data, error } = await supabase.auth.getClaims()
  if (error || !data?.claims) {
    redirect('/auth/login')
  }

  return (
    <div className="p-6 space-y-6">
      <OperatorRinger />
      <OperatorCall />
    </div>
  )
}
