import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { ParticipantPresencePanel } from '@/components/Manager/ParticipantPresencePanel'

export default async function ManagerDashboardPage() {
  const supabase = await createClient()

  const { data, error } = await supabase.auth.getClaims()
  if (error || !data?.claims) {
    redirect('/auth/login')
  }

  return (
    <>
      <ParticipantPresencePanel/>
    </>
  )
}
