import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { UserManagementPanel } from '@/components/Admin/UserManagementPanel'

export default async function AdminPage() {
  const supabase = await createClient()
  const { data, error } = await supabase.auth.getClaims()

  if (error || !data?.claims || !data.claims.app_roles.includes('administrator')) {
    redirect('/auth/login')
  }

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-semibold">Admin Dashboard</h1>
      <UserManagementPanel />
    </div>
  )
}
