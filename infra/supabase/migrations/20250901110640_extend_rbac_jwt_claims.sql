-- Fix ambiguous column reference in custom_access_token_hook by avoiding
-- a variable name that collides with table column names (user_id).
-- Also retains roles & permissions claims as introduced earlier.

CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event JSONB)
RETURNS JSONB
LANGUAGE plpgsql
STABLE
SET search_path = ''
AS $$
DECLARE
  claims JSONB;
  v_user_id UUID;
  roles public.app_role[];
  single_role public.app_role;
  perms TEXT[];
BEGIN
  v_user_id := (event->>'user_id')::uuid;
  claims := event->'claims';

  -- Collect all roles for the user (can be empty)
  SELECT COALESCE(array_agg(ur.role ORDER BY ur.role), ARRAY[]::public.app_role[])
    INTO roles
  FROM public.user_roles ur
  WHERE ur.user_id = v_user_id;

  -- Backward-compatible single role for existing integrations (e.g., Traefik)
  IF array_length(roles, 1) IS NOT NULL AND array_length(roles, 1) > 0 THEN
    single_role := roles[1];
    claims := jsonb_set(claims, '{app_role}', to_jsonb(single_role));
  ELS<PERSON>
    claims := jsonb_set(claims, '{app_role}', 'null');
  END IF;

  -- Compute permissions as the union of permissions granted to all roles
  SELECT COALESCE(
           array_agg(DISTINCT rp.permission::text ORDER BY rp.permission::text),
           ARRAY[]::TEXT[]
         )
    INTO perms
  FROM public.role_permissions rp
  WHERE rp.role = ANY (roles);

  -- Add roles and permissions arrays to claims
  claims := jsonb_set(claims, '{app_roles}', to_jsonb(roles::TEXT[]));
  claims := jsonb_set(claims, '{app_permissions}', to_jsonb(perms));

  -- Persist updated claims back into the event
  event := jsonb_set(event, '{claims}', claims);
  RETURN event;
END;
$$;

GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO supabase_auth_admin;
GRANT SELECT ON TABLE public.role_permissions TO supabase_auth_admin;
GRANT SELECT ON TABLE public.user_roles TO supabase_auth_admin;
