-- Create custom ENUM types for application roles and permissions to enforce consistency.
CREATE TYPE public.app_role AS ENUM ('administrator', 'manager', 'operator', 'translator');
CREATE TYPE public.app_permission AS ENUM (
    'users.create', 'users.read', 'users.update', 'users.delete',
    'calls.assign', 'calls.monitor',
    'reports.generate'
);

-- Table to establish a many-to-many relationship between users and their assigned roles.
CREATE TABLE public.user_roles (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
    role public.app_role NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE (user_id, role) -- Ensures a user cannot be assigned the same role twice.
);
COMMENT ON TABLE public.user_roles IS 'Maps users from auth.users to their application-specific roles.';

-- <PERSON> select permission to the auth admin role
GRANT SELECT ON TABLE public.user_roles TO supabase_auth_admin;

-- Table to define the specific permissions granted to each role.
CREATE TABLE public.role_permissions (
    id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
    role public.app_role NOT NULL,
    permission public.app_permission NOT NULL,
    UNIQUE (role, permission) -- Ensures a permission is not granted to a role more than once.
);
COMMENT ON TABLE public.role_permissions IS 'Defines the matrix of permissions for each application role.';

-- Auth Hook to add the user's application role as a custom claim to the JWT.
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event JSONB)
RETURNS JSONB
LANGUAGE plpgsql
STABLE
SET search_path = ''
AS $$
DECLARE
  claims JSONB;
  user_app_role public.app_role;
BEGIN
  -- Fetch the user's role from the public.user_roles table.
  SELECT role INTO user_app_role FROM public.user_roles WHERE user_id = (event->>'user_id')::uuid;

  claims := event->'claims';

  IF user_app_role IS NOT NULL THEN
    -- If a role is found, set the custom 'app_role' claim in the JWT.
    claims := jsonb_set(claims, '{app_role}', to_jsonb(user_app_role));
  ELSE
    -- If no role is found, explicitly set the claim to null.
    claims := jsonb_set(claims, '{app_role}', 'null');
  END IF;

  -- Update the 'claims' object within the original event payload.
  event := jsonb_set(event, '{claims}', claims);

  -- Return the modified event to be used for JWT generation.
  RETURN event;
END;
$$;

-- Grant necessary permissions for the Auth service to execute the hook.
GRANT EXECUTE ON FUNCTION public.custom_access_token_hook(JSONB) TO supabase_auth_admin;
REVOKE ALL ON FUNCTION public.custom_access_token_hook(JSONB) FROM public, anon, authenticated;