http:
  middlewares:

    # --- CORS Middleware ---

    cors-headers:
      headers:
        accessControlAllowMethods:
          - GET
          - POST
          - PUT
          - DELETE
          - OPTIONS
        accessControlAllowHeaders:
          - Authorization
          - Content-Type
          - Accept
          - X-Requested-With
        accessControlAllowOriginList:
          # Local User Interface
          - "http://localhost:3000"
          - "http://127.0.0.1:3000"
          # Mock SIP Gateway
          - "http://localhost:8010"
          - "http://127.0.0.1:8010"
        accessControlMaxAge: 100
        accessControlAllowCredentials: true
        addVaryHeader: true

    # --- Authentication Middleware ---

    jwt-auth-authenticated:
      plugin:
        jwt:
          #skipPrefetch: true
          secret: super-secret-jwt-token-with-at-least-32-characters-long
          issuers:
            - http://host.docker.internal:54321/auth/v1/
          parameterName: token
          require:
            aud: authenticated

    jwt-auth-role-administrator:
      plugin:
        jwt:
          #skipPrefetch: true
          secret: super-secret-jwt-token-with-at-least-32-characters-long
          issuers:
            - http://host.docker.internal:54321/auth/v1/
          require:
            aud: authenticated
            app_role: administrator

    jwt-auth-role-manager:
      plugin:
        jwt:
          #skipPrefetch: true
          secret: super-secret-jwt-token-with-at-least-32-characters-long
          parameterName: token
          issuers:
            - http://host.docker.internal:54321/auth/v1/
          require:
            aud: authenticated
            app_role: manager