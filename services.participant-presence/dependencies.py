from cortexacommon.security import Security

from context import ApplicationContext
from config import settings


async def get_app_context_dependency() -> ApplicationContext:
    """
    FastAPI dependency to get the application context.
    
    Returns:
        ApplicationContext: The application context instance
    """
    return await ApplicationContext.get_instance()


async def get_security_dependency() -> Security:
    """
    FastAPI dependency to get the security instance.
    
    Returns:
        Security: The security instance
    """
    return Security(settings.security)
