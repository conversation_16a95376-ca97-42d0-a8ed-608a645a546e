from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from cortexacommon.config import KafkaSettings, SecuritySettings


class Settings(BaseSettings):
    host: str = "0.0.0.0"
    port: int = 8001
    debug: bool = False
    supabase_jwt_secret: str = ""
    kafka_settings: KafkaSettings = Field(default_factory=KafkaSettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )


settings = Settings()
