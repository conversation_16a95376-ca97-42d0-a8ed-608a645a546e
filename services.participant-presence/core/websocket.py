from fastapi import WebSocket
from cortexacommon.logging import get_logger

logger = get_logger(__name__)


class ConnectionManager:
    def __init__(self):
        self.active_connections: list[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        """Accepts and stores a new manager's WebSocket connection."""
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info("New manager connected.")

    def disconnect(self, websocket: WebSocket):
        """Removes a manager's WebSocket connection."""
        self.active_connections.remove(websocket)
        logger.info("Manager disconnected.")

    async def broadcast(self, message: str):
        """Sends a message to all connected managers."""
        for connection in self.active_connections:
            await connection.send_text(message)