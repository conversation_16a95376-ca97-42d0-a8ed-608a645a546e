from fastapi import APIRout<PERSON>, Depends, Query, WebSocket, WebSocketDisconnect

from cortexacommon.security import Security, TokenDecodeException
from cortexacommon.logging import get_logger

from context import ApplicationContext
from dependencies import get_app_context_dependency, get_security_dependency
from core.websocket import ConnectionManager
from core.participant import Participant, ParticipantStatus
from .schema import UpdateStatusMessage, ParticipantStatusBroadcast


logger = get_logger(__name__)
router = APIRouter(prefix="/presence")


manager_broadcaster = ConnectionManager()


@router.websocket("/ws/manager")
async def manager_websocket_endpoint(
    websocket: WebSocket,
    token: str = Query(None),
    security: Security = Depends(get_security_dependency),
    context: ApplicationContext = Depends(get_app_context_dependency),
):
    try:
        user = security.authenticate(token)
        if user.app_role not in ["manager", "administrator"]:
            await websocket.close(code=4001, reason="Unauthorized")
            return
    except TokenDecodeException as e:
        logger.warning(f"Manager authentication failed: {e}")
        await websocket.close(code=4001, reason=f"Invalid token: {e}")
        return

    await manager_broadcaster.connect(websocket)

    # Send current participants to manager
    for participant in context.participant_repository.get_participants():
        snapshot_msg = ParticipantStatusBroadcast(participant_id=participant.id, status=participant.status)
        await websocket.send_text(snapshot_msg.model_dump_json())
    logger.info(f"Sent initial participants snapshot to manager {user.sub}")

    try:
        while True:
            await websocket.receive_text()
    except WebSocketDisconnect:
        logger.info(f"Manager {user.sub} disconnected cleanly.")
    finally:
        manager_broadcaster.disconnect(websocket)


@router.websocket("/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    token: str = Query(None),
    context: ApplicationContext = Depends(get_app_context_dependency),
    security: Security = Depends(get_security_dependency)
):
    try:
        user = security.authenticate(token)
    except TokenDecodeException as e:
        logger.warning(f"Participant authentication failed: {e}")
        await websocket.close(code=4001, reason=f"Invalid token: {e}")
        return

    await websocket.accept()

    # --- Utility function to broadcast status updates ---
    async def notify_managers_of_status_update(p_id: str, status: ParticipantStatus):
        broadcast_message = ParticipantStatusBroadcast(participant_id=p_id, status=status)
        await manager_broadcaster.broadcast(broadcast_message.model_dump_json())
        logger.info(f"Broadcasted status '{status.value}' for participant {p_id} to managers.")

    # Manage participant connection and initial status
    participant = context.participant_repository.get_participant(user.sub)
    if not participant:
        participant = Participant(user.sub, user.app_role, ParticipantStatus.AVAILABLE)
        context.participant_repository.add_participant(participant)
        logger.info(f"New participant added: {participant}")
    else:
        logger.info(f"Existing participant connected: {participant}")
        context.participant_repository.update_participant_status(user.sub, ParticipantStatus.AVAILABLE)

    # Broadcast initial 'AVAILABLE' status
    await notify_managers_of_status_update(user.sub, ParticipantStatus.AVAILABLE)

    try:
        # Keep the connection alive and handle incoming messages
        while True:
            data = await websocket.receive_text()
            message = UpdateStatusMessage.model_validate_json(data)
            context.participant_repository.update_participant_status(user.sub, message.status)
            # Broadcast status update from message
            await notify_managers_of_status_update(user.sub, message.status)

    except WebSocketDisconnect:
        logger.info(f"Participant disconnected cleanly: {participant}")
    except Exception as e:
        logger.error(f"An error occurred with participant {user.sub}: {e}")
    finally:
        # Set participant as OFFLINE and broadcast the final status
        context.participant_repository.update_participant_status(user.sub, ParticipantStatus.OFFLINE)
        await notify_managers_of_status_update(user.sub, ParticipantStatus.OFFLINE)