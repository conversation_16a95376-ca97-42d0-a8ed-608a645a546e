from fastapi import APIRouter, Depends, Query
from cortexacommon.logging import get_logger

from context import ApplicationContext
from dependencies import get_app_context_dependency
from core.participant import Participant

logger = get_logger(__name__)
router = APIRouter(prefix="/internal")


@router.get("/participants")
def get_available_participants(
    role: str = Query(None),
    status: str = Query(None),
    context: ApplicationContext = Depends(get_app_context_dependency)
) -> list[Participant]:
    return context.participant_repository.get_participants(role, status)
