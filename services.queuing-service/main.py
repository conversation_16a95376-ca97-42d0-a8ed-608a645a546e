import asyncio

from cortexacommon.logging import get_logger

from context import ApplicationContext
from consumers.call_started import initialize_consumer as initialize_call_started_consumer

logger = get_logger(__name__)

async def main() -> None:
    context = await ApplicationContext.get_instance()
    await context.initialize()

    # Initialize consumer
    consumer, consume_task = await initialize_call_started_consumer(context.queue_handler)

    try:
        await asyncio.Event().wait()
    except KeyboardInterrupt:
        pass
    finally:
        await consumer.stop()
        consume_task.cancel()
        await context.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
