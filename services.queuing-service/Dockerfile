FROM python:3.12-slim

WORKDIR /app

# Install Poetry
RUN pip install --no-cache-dir poetry

# Copy shared common first to leverage Docker layer caching
COPY shared/cortexa-common /app/shared/cortexa-common

# Copy service files
COPY services.queuing-service /app/services.queuing-service

WORKDIR /app/services.queuing-service

# Install dependencies
RUN poetry install --no-interaction --no-ansi

# Expose port
EXPOSE 8000

# Run the service
CMD ["poetry", "run", "python3", "main.py"]

