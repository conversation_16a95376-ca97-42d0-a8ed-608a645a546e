from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict

from cortexacommon.config import KafkaSettings


class Settings(BaseSettings):
    host: str = "0.0.0.0"
    port: int = 8003
    debug: bool = False
    service_name: str = "queuing-service"

    kafka_settings: KafkaSettings = Field(default_factory=KafkaSettings)
    participant_service_base_url: str = Field(default="http://localhost:8001", description="Base URL of the participant presence service")
    assignment_retry_attempts: int = Field(default=5, description="Number of times to retry operator lookup")
    assignment_retry_interval_ms: int = Field(default=1500, description="Delay between retries in milliseconds")

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )


settings = Settings()
