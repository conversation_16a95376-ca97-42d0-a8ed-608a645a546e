from typing import List
import httpx

from cortexacommon.logging import get_logger

logger = get_logger(__name__)


class ParticipantPresenceClient:
    def __init__(self, base_url: str):
        self._client = httpx.AsyncClient(base_url=base_url.rstrip("/"))

    async def list_available_operators(self, timeout: float = 3.0) -> List[dict]:
        # Calls GET /internal/participants?role=operator&status=available
        resp = await self._client.get(
            "/internal/participants",
            params={"role": "operator", "status": "available"},
            timeout=timeout,
        )

        resp.raise_for_status()
        data = resp.json()
        logger.info(f"Available operators: {data}")

        # Expect list of {id, role, status}
        if not isinstance(data, list):
            logger.warning("Unexpected response from participant service: {data}")
            return []
        return data

    async def close(self):
        await self._client.aclose()

