from typing import Any
import random
import asyncio

from cortexacommon.logging import get_logger
from cortexacommon.events.producer import EventProducer
from cortexacommon.events.schemas import CallQueuedEvent, CallAssignedEvent, CallStartedEvent

from services.participant_presence import ParticipantPresenceClient

logger = get_logger(__name__)


class CallQueueHandler:
    def __init__(self, presence_client: ParticipantPresenceClient, producer: EventProducer, *,
                 max_attempts: int = 5, interval_ms: int = 500):
        self._presence_client = presence_client
        self._producer = producer
        self._max_attempts = max_attempts
        self._interval_ms = interval_ms

    async def handle_call_started(self, payload: dict[str, Any]) -> None:
        """
        Handle call-orchestrator.call-started events.
        1) Publish queuing-service.call-queued event
        2) Fetch available operators (with retry)
        3) Assign call to one using simple algorithm
        4) Publish call.assigned event
        """
        event = CallStartedEvent(**payload)
        call_id = event.call_id
        logger.info(f"Call started received for call_id={call_id}")

        # 1) Publish queued event
        queued_event = CallQueuedEvent(call_id=call_id, source_service="queuing-service")
        await self._producer.publish_event("queuing-service.call-queued", queued_event)

        # 2) Fetch available operators with basic retry/backoff
        operators: list[dict] = []
        attempt = 0
        while attempt < self._max_attempts:
            attempt += 1
            try:
                operators = await self._presence_client.list_available_operators()
                if operators:
                    break
                logger.info(f"No available operators yet for call {call_id} (attempt {attempt}/{self._max_attempts})")
            except Exception as e:
                logger.warning(f"Operator lookup failed for call {call_id} on attempt {attempt}/{self._max_attempts}: {e}")
            if attempt < self._max_attempts:
                await asyncio.sleep(self._interval_ms / 1000.0)

        if not operators:
            logger.warning(f"Giving up: no available operators found for call {call_id} after {self._max_attempts} attempts")
            # Probably should announce this somewhere to avoid lost routing of calls
            return

        # 3) Simple assignment algorithm: random choice for now
        chosen = random.choice(operators)
        operator_id = chosen.get("id") or chosen.get("participant_id")
        if not operator_id:
            logger.error(f"Operator object missing id: {chosen}")
            return

        # 4) Publish call.assigned event
        assigned_event = CallAssignedEvent(call_id=call_id, operator_id=operator_id, source_service="queuing-service")
        await self._producer.publish_event("call.assigned", assigned_event)

        logger.info(f"Assigned call {call_id} to operator {operator_id}")
