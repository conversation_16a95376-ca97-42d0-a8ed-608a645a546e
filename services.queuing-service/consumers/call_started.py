import asyncio

from cortexacommon.events.consumer import EventConsumer
from cortexacommon.logging import get_logger

from config import settings
from services.call_queue_handler import CallQueueHandler

logger = get_logger(__name__)


async def initialize_consumer(queue_handler: CallQueueHandler) -> tuple[EventConsumer, asyncio.Task]:
    consumer = EventConsumer(
        kafka_settings=settings.kafka_settings,
        topics=["call-orchestrator.call-started"],
    )
    await consumer.start()

    consumer.register_handler("call-orchestrator.call-started", queue_handler.handle_call_started)

    # Start consuming
    consume_task = asyncio.create_task(consumer.consume())
    logger.info("Queuing service initialized and consuming events")
    return consumer, consume_task
