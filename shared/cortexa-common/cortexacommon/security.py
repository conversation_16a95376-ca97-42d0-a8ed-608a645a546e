from pydantic import BaseModel
from typing import TypedDict
from jose import jwt, JW<PERSON>rror
from cortexacommon.config import SecuritySettings


class User(BaseModel):
    sub: str
    app_role: str
    app_permissions: list[str]


class TokenDecodeException(Exception): ...


class Security:
    def __init__(self, settings: SecuritySettings):
        self.settings = settings

    def authenticate(self, token: str) -> User:
        try:
            payload = jwt.decode(token, self.settings.jwt_secret, algorithms=["HS256"],
                                 audience="authenticated", options={"verify_signature": False})
            return User(**payload)

        except JWTError as e:
            raise TokenDecodeException(f"Failed to decode token: {e}") from e
        except Exception as e:
            raise TokenDecodeException(f"Authentication failed: {e}") from e
    