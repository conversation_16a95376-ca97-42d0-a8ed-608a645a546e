import asyncio
import json
from typing import Any, Callable
from aiokafka import AIOKafkaConsumer, ConsumerRecord

from ..config import KafkaSettings
from ..logging import get_logger

logger = get_logger(__name__)


class EventConsumer:
    """Kafka event consumer for consuming events."""

    def __init__(
        self,
        kafka_settings: KafkaSettings,
        topics: list[str],
        group_id: str | None = None
    ):
        """Initialize the event consumer."""
        self.kafka_settings = kafka_settings
        self.topics = topics
        self.group_id = group_id or kafka_settings.group_id
        self.consumer: AIOKafkaConsumer | None = None
        self.handlers: dict[str, Callable] = {}
        self._running = False

    async def start(self) -> None:
        """Start the Kafka consumer."""
        self.consumer = AIOKafkaConsumer(
            *self.topics,
            bootstrap_servers=self.kafka_settings.bootstrap_servers,
            group_id=self.group_id,
            value_deserializer=lambda m: json.loads(m.decode('utf-8')) if m else None,
            key_deserializer=lambda k: k.decode('utf-8') if k else None,
            auto_offset_reset='latest',
        )
        await self.consumer.start()
        logger.info(f"Kafka consumer started for topics: {self.topics}")

    async def stop(self) -> None:
        """Stop the Kafka consumer."""
        self._running = False
        if self.consumer:
            await self.consumer.stop()
            logger.info("Kafka consumer stopped")

    def register_handler(self, topic: str, handler: Callable[[dict[str, Any]], Any]) -> None:
        """Register a handler for a specific topic."""
        self.handlers[topic] = handler
        logger.info(f"Registered handler for topic: {topic}")

    async def consume(self) -> None:
        """Start consuming messages."""
        if not self.consumer:
            raise RuntimeError("Consumer not started. Call start() first.")

        self._running = True
        logger.info("Starting message consumption")

        try:
            async for message in self.consumer:
                if not self._running:
                    break
                try:
                    await self._handle_message(message)
                except Exception as e:
                    logger.error(f"Error handling message: {e}")
        except Exception as e:
            logger.error(f"Error in consumer loop: {e}")
            raise

    async def _handle_message(self, message: ConsumerRecord) -> None:
        """Handle a single message."""
        topic = message.topic
        value = message.value

        logger.debug(f"Received message from topic {topic}: {value}")

        # Find handler for this topic
        handler = self.handlers.get(topic)
        if not handler:
            logger.warning(f"No handler registered for topic: {topic}")
            return

        try:
            # Call the handler
            if asyncio.iscoroutinefunction(handler):
                await handler(value)
            else:
                handler(value)
        except Exception as e:
            logger.error(f"Handler error for topic {topic}: {e}")
            raise

    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.stop()
        return None
