"""
Enhanced logging configuration using loguru for Cortexa services.

This module provides structured logging with JSON formatting, correlation ID support,
and service-specific context for consistent logging across all microservices.
"""

import sys
from loguru import logger
from pydantic import BaseModel


class LogConfig(BaseModel):
    """Configuration for logging setup."""
    
    service_name: str
    level: str = "INFO"
    format_json: bool = False
    log_file: str | None = None
    max_file_size: str = "100 MB"
    retention: str = "30 days"
    compression: str = "gz"
    colorize: bool = True
    diagnose: bool = False


# Text formatter as format string
def get_text_format() -> str:
    """Get text format string for loguru."""
    return (
        "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )


def setup_logging(config: LogConfig) -> None:
    """
    Set up loguru logging with the specified configuration.

    Args:
        config: Logging configuration
    """
    # Remove default handler
    logger.remove()

    # Determine log level
    log_level = config.level.upper()

    logger.add(
        sys.stderr,
        format=get_text_format(),
        level=log_level,
        colorize=config.colorize,
        diagnose=config.diagnose,
        enqueue=True,              # Thread-safe logging
        catch=True,                # Catch exceptions in logging
        serialize=False,           # Never serialize console output
    )

    # Configure file handler if specified
    if config.log_file:
        logger.add(
            config.log_file,
            format="{message}",
            level=log_level,
            rotation=config.max_file_size,
            retention=config.retention,
            compression=config.compression,
            enqueue=True,
            catch=True,
            serialize=config.format_json,
        )

    # Add service name to all log records
    logger.configure(extra={"service_name": config.service_name})

    # Log the configuration
    logger.info(
        "Logging configured",
        service=config.service_name,
        level=log_level,
        json_format=config.format_json,
        file_logging=config.log_file is not None,
    )


def get_logger(name: str):
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        Logger instance
    """
    return logger.bind(logger_name=name)


# Convenience function for quick setup
def setup_service_logging(
    service_name: str,
    level: str = "INFO",
    json_format: bool = False,
    log_file: str | None = None,
) -> None:
    """
    Quick setup for service logging with sensible defaults.
    
    Args:
        service_name: Name of the service
        level: Log level
        json_format: Use JSON format
        log_file: Optional log file path
    """
    config = LogConfig(
        service_name=service_name,
        level=level,
        format_json=json_format,
        log_file=log_file,
        colorize=sys.stderr.isatty(),  # Only colorize if terminal
    )
    
    setup_logging(config)
