from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from cortexacommon.config import SecuritySettings


class Settings(BaseSettings):

    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    supabase_api_url: str = ""
    supabase_service_role_key: str = ""
    security: SecuritySettings = Field(default_factory=SecuritySettings)

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )


settings = Settings()
