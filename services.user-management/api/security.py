from fastapi import HTT<PERSON><PERSON>x<PERSON>, status, Request

from cortexacommon.security import Security, TokenDecodeException

from config import settings


def require_permission(permission: str):
    """
    A dependency that checks if the user's JWT contains the required permission.
    """
    def dependency(request: Request) -> None:
        token = request.headers.get("Authorization")
        if not token or not token.startswith("Bearer "):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Not authenticated",
            )

        token = token.replace("Bearer ", "")

        try:
            security = Security(settings.security)
            user = security.authenticate(token)
            if permission not in user.app_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Missing required permission: {permission}",
                )
        except TokenDecodeException as e:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail=f"Could not validate credentials: {e}",
            )

    return dependency
