from fastapi import <PERSON><PERSON>outer, Depends, status

from context import <PERSON>Context
from dependencies import get_app_context_dependency
from .user_models import UserCreate, UserResponse
from .user_controller import UserController
from ..security import require_permission


router = APIRouter(
    prefix="/users",
    tags=["users"],
    responses={404: {"description": "Not found"}},
)


async def get_user_controller(
    context: ApplicationContext = Depends(get_app_context_dependency)
) -> UserController:
    """FastAPI dependency to get the user controller instance."""
    return UserController(context)


@router.post(
    "",
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new user",
    description="Creates a new user and assigns them a role. This is an admin-only operation.",
    dependencies=[Depends(require_permission("users.create"))]
)
async def create_user(
    user_data: UserCreate,
    controller: UserController = Depends(get_user_controller)
) -> UserResponse:
    """
    Create a new user and assign them a role.
    
    Args:
        user_data: The user creation data
        controller: The user controller (injected by FastAPI)
        
    Returns:
        UserResponse: The created user information
    """
    return await controller.create_user(user_data)


@router.put(
    "/{user_id}/roles/{role_name}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Assign role to user",
    description="Assigns an additional role to an existing user. This is an admin-only operation.",
    dependencies=[Depends(require_permission("users.update"))]
)
async def assign_role_to_user(
    user_id: str,
    role_name: str,
    controller: UserController = Depends(get_user_controller)
) -> None:
    """
    Assign an additional role to an existing user.
    
    Args:
        user_id: The ID of the user to assign the role to
        role_name: The name of the role to assign
        controller: The user controller (injected by FastAPI)
    """
    await controller.update_user_role(user_id, role_name)


@router.get(
    "",
    response_model=list[UserResponse],
    summary="Get all users",
    description="Retrieves all users and their roles. This is an admin-only operation.",
    dependencies=[Depends(require_permission("users.read"))]
)
async def get_users(
    controller: UserController = Depends(get_user_controller)
) -> list[UserResponse]:
    """
    Get all users.
    
    Args:
        controller: The user controller (injected by FastAPI)
        
    Returns:
        List of UserResponse objects
    """
    return await controller.get_users()

@router.delete(
    "/{user_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete user",
    description="Deletes a user. This is an admin-only operation.",
    dependencies=[Depends(require_permission("users.delete"))]
)
async def delete_user(
    user_id: str,
    controller: UserController = Depends(get_user_controller)
) -> None:
    """
    Delete a user.
    
    Args:
        user_id: The ID of the user to delete
        controller: The user controller (injected by FastAPI)
    """
    await controller.delete_user(user_id)