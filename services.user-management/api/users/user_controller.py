from supabase_auth.types import User
from supabase_auth.errors import AuthApiError
from fastapi import HTTPException

from context import ApplicationContext
from .user_models import UserCreate, UserResponse


class UserController:
    """
    Controller class for user management operations.
    
    This class contains the business logic for user operations,
    keeping it separate from the API routing layer.
    """
    
    def __init__(self, context: ApplicationContext):
        """
        Initialize the user controller.
        
        Args:
            context: The application context containing shared resources.
        """
        self.context = context
    
    async def create_user(self, user_data: UserCreate) -> UserResponse:
        """
        Create a new user and assign them a role.
        
        Args:
            user_data: The user creation data including email, password, and role.
            
        Returns:
            UserResponse: The created user information.
            
        Raises:
            HTTPException: If user creation or role assignment fails.
        """
        # 1. Create the user in Supabase Auth
        # We auto-confirm the email as it's created by an admin.
        try:
            new_user_response = self.context.supabase.auth.admin.create_user({
                "email": user_data.email,
                "password": user_data.password,
                "email_confirm": True,
            })
        except AuthApiError as e:
            raise HTTPException(status_code=400, detail=e)
        
        new_user = new_user_response.user
        if not new_user:
            raise HTTPException(
                status_code=400, 
                detail="User creation failed in Supabase Auth."
            )

        # 2. Assign the role in the 'user_roles' table.
        # The 'role' column now directly accepts the role name as an ENUM value.
        self.context.supabase.table("user_roles").insert({
            "user_id": new_user.id,
            "role": user_data.role
        }).execute()

        return UserResponse(
            id=new_user.id,
            role=user_data.role,
            email=new_user.email,
            created_at=new_user.created_at.isoformat()
        )
    
    async def update_user_role(self, user_id: str, role_name: str) -> None:
        """
        Updates the role for an existing user.
        
        This assumes a user can only have one role at a time, as per the JWT hook design.
        
        Args:
            user_id: The UUID of the user whose role is to be updated.
            role_name: The new role to assign (e.g., 'operator', 'manager').
            
        Raises:
            HTTPException: If the user is not found or the role update fails.
        """
        # Update the user's role in the user_roles table.
        response = self.context.supabase.table("user_roles").update({
            "role": role_name
        }).eq("user_id", user_id).execute()
        
        # The update operation returns the updated rows in the data attribute.
        # If no rows were updated, it means the user_id was not found in the table.
        if not response.data:
            raise HTTPException(
                status_code=404, 
                detail=f"User with ID '{user_id}' not found or no role to update."
            )
        
    async def get_users(self) -> list[UserResponse]:
        """
        Get all users.
        
        Returns:
            List of UserResponse objects.
            
        Raises:
            HTTPException: If the database query fails.
        """
        users: list[User] = self.context.supabase.auth.admin.list_users()
        roles = self.context.supabase.table("user_roles").select(
            "user_id, role"
        ).execute()

        print({"users": users})
        print(roles)

        return [
            UserResponse(
                id=user.id,
                role=next((role["role"] for role in roles.data if role["user_id"] == user.id), None),
                email=user.email,
                created_at=user.created_at.isoformat()
            )
            for user in users
        ]

    async def delete_user(self, user_id: str) -> None:
        """
        Delete a user.
        
        Args:
            user_id: The UUID of the user to delete.
            
        Raises:
            HTTPException: If the user is not found or the deletion fails.
        """
        try:
            self.context.supabase.auth.admin.delete_user(user_id)
        except AuthApiError as e:
            raise HTTPException(status_code=400, detail=e)