import asyncio
from typing import Optional

from supabase import create_client, Client

from config import settings


class ApplicationContext:
    """
    Application context for managing shared resources.

    This class provides a singleton pattern for managing the Supabase client
    and other application-wide resources.
    """

    _instance: Optional['ApplicationContext'] = None
    _lock = asyncio.Lock()

    def __init__(self):
        """Initialize the application context."""
        self._supabase_client: Optional[Client] = None
        self._initialized = False

    @classmethod
    async def get_instance(cls) -> 'ApplicationContext':
        """
        Get the singleton instance of the application context.

        Returns:
            ApplicationContext: The singleton instance
        """
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    @property
    def supabase(self) -> Client:
        """
        Get the Supabase client instance.

        Returns:
            Client: The Supabase client instance

        Raises:
            AssertionError: If the client is not initialized
        """
        assert self._supabase_client is not None, "Supabase client not initialized"
        return self._supabase_client

    @property
    def is_initialized(self) -> bool:
        """
        Check if the application context is initialized.

        Returns:
            bool: True if initialized, False otherwise
        """
        return self._initialized

    async def initialize(self) -> None:
        """
        Initialize the application context and its resources.

        This method initializes the Supabase client and other shared resources.
        """
        if self._initialized:
            return

        try:
            # Initialize Supabase client
            self._supabase_client = create_client(
                settings.supabase_api_url,
                settings.supabase_service_role_key
            )

            # Test the connection to ensure it's working
            # This implements the fail-early principle by testing on startup
            try:
                # Simple query to test the connection
                self._supabase_client.table("user_roles").select("*").execute()
            except Exception as e:
                raise RuntimeError(f"Failed to connect to Supabase: {e}")

            self._initialized = True

        except Exception as e:
            self._initialized = False
            raise RuntimeError(f"Failed to initialize application context: {e}")

    async def cleanup(self) -> None:
        """
        Clean up the application context and its resources.

        This method should be called during application shutdown.
        """
        try:
            if self._supabase_client:
                # Supabase client doesn't require explicit cleanup
                # but we set it to None to allow garbage collection
                self._supabase_client = None

            self._initialized = False

        except Exception as e:
            # Log error but don't raise to avoid issues during shutdown
            print(f"Error during application context cleanup: {e}")

    @classmethod
    async def reset_instance(cls) -> None:
        """
        Reset the singleton instance (mainly for testing).

        This method should only be used in test scenarios.
        """
        async with cls._lock:
            if cls._instance:
                await cls._instance.cleanup()
            cls._instance = None


# Global function to get the application context
async def get_app_context() -> ApplicationContext:
    """
    Get the application context instance.

    Returns:
        ApplicationContext: The application context instance
    """
    return await ApplicationContext.get_instance()