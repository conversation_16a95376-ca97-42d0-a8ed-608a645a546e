from dataclasses import dataclass

from cortexacommon.logging import get_logger
from cortexacommon.events.producer import EventProducer
from cortexacommon.events.consumer import EventConsumer

from config import settings
from core.s2st import S2STProcessor
from services.voice_router import VoiceRouterClient

logger = get_logger(__name__)


@dataclass
class ApplicationContext:
    s2st_processor: S2STProcessor
    event_producer: EventProducer
    event_consumer: EventConsumer
    voice_router_client: VoiceRouterClient

    @classmethod
    async def create(cls, consumer_topics: list[str]) -> "ApplicationContext":
        # Initialize event publisher
        logger.info("Creating event publisher...")
        event_producer = EventProducer(settings.kafka)
        await event_producer.start()

        # Initialize event consumer
        logger.info("Creating event consumer...")
        event_consumer = EventConsumer(
            kafka_settings=settings.kafka,
            topics=consumer_topics,
        )

        # Initialize S2ST processor
        logger.info("Creating S2ST processor...")
        s2st_processor = await S2STProcessor.create(event_producer)

        # Initialize the Voice Router client
        voice_router_client = VoiceRouterClient(settings.voice_router_url)

        logger.info("Application context initialized successfully")

        # Return a fully initialized instance of the class
        return cls(
            s2st_processor=s2st_processor,
            event_producer=event_producer,
            event_consumer=event_consumer,
            voice_router_client=voice_router_client,
        )

    async def cleanup(self) -> None:
        logger.info("Cleaning up application context...")

        if self.event_producer:
            await self.event_producer.stop()
            logger.info("Event publisher cleaned up")

        if self.event_consumer:
            await self.event_consumer.stop()
            logger.info("Event consumer cleaned up")

        logger.info("Application context cleanup complete")

