from openai import Async<PERSON>penAI

from cortexacommon.logging import get_logger

from config import settings
from .base import BaseTTSProvider

logger = get_logger(__name__)


class OpenAITTSProvider(BaseTTSProvider):
    """Text-to-Speech provider using OpenAI TTS API."""
    
    def __init__(self, 
                 api_key: str | None = None,
                 model: str | None = None,
                 voice: str | None = None,
                 **kwargs):
        """
        Initialize OpenAI TTS provider.
        
        Args:
            api_key: OpenAI API key
            model: TTS model identifier
            voice: Voice identifier
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.api_key = api_key or settings.tts_api_key
        self.model = model or settings.tts_model
        self.voice = voice or settings.tts_voice
        self._client: AsyncOpenAI | None = None
    
    async def initialize(self) -> None:
        """Initialize TTS client."""
        if not self.api_key:
            logger.warning("OpenAI API key not provided, TTS not configured")
            self._client = None
            self._initialized = False
            return
        
        try:
            self._client = AsyncOpenAI(api_key=self.api_key)
            self._initialized = True
            logger.info("OpenAI TTS client initialized")
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI TTS client: {e}")
            self._client = None
            self._initialized = False
    
    async def synthesize(self, text: str) -> bytes | None:
        """
        Synthesize speech from text.
        
        Args:
            text: Text to synthesize
            
        Returns:
            Audio bytes or None if synthesis fails
        """
        assert self._client is not None, "OpenAI TTS client not initialized"

        if not text.strip():
            return None
        
        try:
            response = await self._client.audio.speech.create(
                model=self.model,
                voice=self.voice,
                input=text,
                response_format="wav",
            )
            
            return response.content
            
        except Exception as e:
            logger.error(f"TTS error: {e}")
            return None
