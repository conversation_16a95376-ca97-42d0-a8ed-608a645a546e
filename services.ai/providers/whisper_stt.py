import asyncio
import numpy as np
from faster_whisper import WhisperModel

from cortexacommon.logging import get_logger

from config import settings
from .base import BaseSTTProvider


logger = get_logger(__name__)


class WhisperSTTProvider(BaseSTTProvider):
    """Speech-to-Text provider using Faster Whisper."""
    
    def __init__(self, 
                 model_size: str | None = None,
                 compute_type: str | None = None,
                 device: str | None = None,
                 **kwargs):
        """
        Initialize Whisper STT provider.
        
        Args:
            model_size: Whisper model size (tiny, base, small, medium, large)
            compute_type: Compute type (int8, int16, float16, float32)
            device: Device for model (cpu, cuda)
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.model_size = model_size or settings.whisper_model_size
        self.compute_type = compute_type or settings.whisper_compute_type
        self.device = device or settings.whisper_device
        self._model: WhisperModel | None = None
    
    async def initialize(self) -> None:
        """Initialize Faster Whisper model."""
        try:
            self._model = WhisperModel(
                self.model_size,
                device=self.device,
                compute_type=self.compute_type,
            )
            
            self._initialized = True
            logger.info(f"Whisper model {self.model_size} initialized on {self.device}")
        except Exception as e:
            logger.error(f"Failed to initialize Whisper model: {e}")
            self._model = None
            self._initialized = False

    async def transcribe(self, audio_data: bytes) -> tuple[str, float]:
        """
        Transcribes audio bytes into text using the Whisper model.

        The raw audio bytes are expected to be 16-bit PCM at 16kHz (mono).
        The blocking transcription model is run in a thread pool to prevent
        blocking the main service.

        Args:
            audio: A bytes object containing the raw PCM audio data.

        Returns:
            A tuple containing the transcribed text and a confidence score (0-1),
            or None if transcription fails or produces no text.
        """
        assert self._model is not None, "Whisper model not initialized"

        # 1. Convert audio bytes to a NumPy array of 32-bit floats.
        # Whisper expects audio as a float32 array normalized between -1.0 and 1.0.
        # The incoming audio is 16-bit signed integers (s16le).
        audio_np = np.frombuffer(audio_data, dtype=np.int16).astype(np.float32) / 32768.0

        loop = asyncio.get_running_loop()

        # 2. Run the blocking, CPU/GPU-bound transcribe function in a thread pool
        # executor to avoid blocking the main event loop.
        result = await loop.run_in_executor(
            None,  # Use the default thread pool executor
            self._blocking_transcribe,
            audio_np
        )

        if result:
            text, confidence = result
            logger.info(f"Transcription: '{text}' (Confidence: {confidence:.2f})")
            return text, confidence
        
        return "", 0.0
    
    def _blocking_transcribe(self, audio_np: np.ndarray) -> tuple[str, float] | None:
        """
        Performs the blocking transcription operation.
        This method is designed to be run in a separate thread to avoid blocking
        the asyncio event loop.
        """
        assert self._model is not None, "Whisper model not initialized"
        segments, _ = self._model.transcribe(
            audio_np,
            beam_size=1,
            language="en",
            condition_on_previous_text=False,
            temperature=0.0,
        )

        # Consume the generator to get all segments
        segment_list = list(segments)
        if not segment_list:
            return None

        full_text = "".join(segment.text for segment in segment_list).strip()
        if not full_text:
            return None

        # Calculate confidence as the exponentiated average log probability
        total_logprob = sum(s.avg_logprob for s in segment_list)
        avg_logprob = total_logprob / len(segment_list)
        confidence = float(np.exp(avg_logprob))

        return full_text, confidence