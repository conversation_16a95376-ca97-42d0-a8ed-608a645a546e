from webrtcvad import Vad

from cortexacommon.logging import get_logger

from config import settings
from .base import BaseVADProvider

logger = get_logger(__name__)


class WebRTCVADProvider(BaseVADProvider):
    """Voice Activity Detection provider using WebRTC VAD."""
    
    def __init__(self, aggressiveness: int = 3, frame_duration_ms: int = 30, **kwargs):
        """
        Initialize WebRTC VAD provider.
        
        Args:
            aggressiveness: VAD aggressiveness level (0-3)
            frame_duration_ms: Frame duration in milliseconds
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.aggressiveness = aggressiveness
        self.frame_duration_ms = frame_duration_ms
        self._frame_size = int(settings.audio_sample_rate * frame_duration_ms / 1000)
        self._vad: Vad | None = None
    
    async def initialize(self) -> None:
        """Initialize WebRTC VAD."""
        self._vad = Vad(self.aggressiveness)
        self._initialized = True
        logger.info(f"WebRTC VAD initialized with aggressiveness {self.aggressiveness}")

    
    def is_speech(self, audio_frame: bytes) -> bool:
        """
        Detect if audio frame contains speech.
        
        Args:
            audio_frame: Audio frame bytes (16kHz, 16-bit, mono)
            
        Returns:
            bool: True if speech detected
        """
        assert self._vad is not None, "WebRTC VAD not initialized"

        # Ensure frame is correct size
        if len(audio_frame) != self.frame_size * 2:  # 2 bytes per sample
            return False
        
        return self._vad.is_speech(audio_frame, settings.audio_sample_rate)

    
    @property
    def frame_size(self) -> int:
        """Get the required frame size for VAD processing."""
        return self._frame_size
