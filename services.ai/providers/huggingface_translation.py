from transformers import pipeline, Pipeline

from cortexacommon.logging import get_logger

from config import settings
from .base import BaseTranslationProvider

logger = get_logger(__name__)


class HuggingFaceTranslationProvider(BaseTranslationProvider):
    """Translation provider using Hugging Face transformers."""
    
    def __init__(self, 
                 model: str | None = None,
                 device: str | None = None,
                 **kwargs):
        """
        Initialize HuggingFace Translation provider.
        
        Args:
            model: Translation model identifier
            device: Device for model (cpu, cuda)
            **kwargs: Additional configuration parameters
        """
        super().__init__(**kwargs)
        self.model = model or settings.translation_model
        self.device = device or settings.translation_device
        self._pipeline: Pipeline | None = None
    
    async def initialize(self) -> None:
        """Initialize translation model."""
        try:
            self._pipeline = pipeline(
                "translation",
                model=self.model,
                device=0 if self.device == "cuda" else -1,
            )

            self._initialized = True
            logger.info(f"Translation model {self.model} initialized on {self.device}")
        except Exception as e:
            logger.error(f"Failed to initialize translation model: {e}")
            self._pipeline = None
            self._initialized = False
    
    async def translate(self, text: str, source_language: str, target_language: str) -> str:
        """
        Translate text to target language.
        
        Args:
            text: Text to translate
            
        Returns:
            Translated text
        """
        assert self._pipeline is not None, "Translation model not initialized"

        if not text.strip():
            return text
        
        try:
            result = self._pipeline(text, max_length=512)
            if result and len(result) > 0:
                return list(result)[0]['translation_text']
            
            return text
            
        except Exception as e:
            logger.error(f"Translation error: {e}")
            return text
