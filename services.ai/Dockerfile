FROM python:3.12-slim

WORKDIR /app

# Install dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install --no-cache-dir poetry

# Copy shared common first to leverage Docker layer caching
COPY shared/cortexa-common /app/shared/cortexa-common

# Copy service files
COPY services.ai /app/services.ai

WORKDIR /app/services.ai

# Install dependencies
RUN poetry install --no-interaction --no-ansi

# Run the service
CMD ["poetry", "run", "python3", "main.py"]
