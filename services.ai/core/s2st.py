import asyncio
from dataclasses import dataclass
from uuid import uuid4

from cortexacommon.events.producer import EventProducer
from cortexacommon.events.schemas import TranscriptionEvent
from cortexacommon.logging import get_logger

from config import settings
from providers.base import (BaseSTTProvider, BaseTTSProvider,
                              BaseTranslationProvider, BaseVADProvider)
from providers.factory import ProviderFactory

logger = get_logger(__name__)


@dataclass
class S2STProcessor:
    """
    Orchestrates the full Speech-to-Speech Translation (S2ST) pipeline.
    This includes voice activity detection, speech-to-text, translation,
    and text-to-speech.
    """
    events_producer: EventProducer
    vad: BaseVADProvider
    stt: BaseSTTProvider
    translation: BaseTranslationProvider
    tts: BaseTTSProvider
    target_language: str = "en"
    source_language: str = "sp" # TODO: change me later on auto detect

    @classmethod
    async def create(cls, events_producer: EventProducer) -> "S2STProcessor":
        """
        Asynchronously creates and initializes an S2STProcessor with all
        necessary AI providers.
        """
        # Initialize Voice Activity Detection (VAD) Provider
        vad = ProviderFactory.create_vad_provider(
            settings.vad_provider,
            aggressiveness=settings.vad_aggressiveness,
            sample_rate=settings.audio_sample_rate,
            frame_duration_ms=settings.vad_frame_duration_ms
        )

        # Initialize Speech-to-Text (STT) Provider
        stt = ProviderFactory.create_stt_provider(
            settings.stt_provider,
            model_size=settings.whisper_model_size,
            compute_type=settings.whisper_compute_type,
            device=settings.whisper_device
        )

        # Initialize Translation Provider
        translation = ProviderFactory.create_translation_provider(
            settings.translation_provider
        )

        # Initialize Text-to-Speech (TTS) Provider
        tts = ProviderFactory.create_tts_provider(
            settings.tts_provider
        )

        # Await all initializations concurrently for faster startup
        await asyncio.gather(
            vad.initialize(),
            stt.initialize(),
            translation.initialize(),
            tts.initialize()
        )

        logger.info("S2ST Processor initialized successfully")

        return cls(
            events_producer=events_producer,
            vad=vad,
            stt=stt,
            translation=translation,
            tts=tts
        )

    async def start_pipeline_process(self, audio_data: bytes, participant_id: str, call_id: str) -> None:
        """
        Starts the full S2ST pipeline for a given audio segment.
        1. Speech-to-Text (STT)
        2. Translation (if source language differs from target)
        3. Text-to-Speech (TTS) on translated text
        4. Publishes events at each stage.
        """
        # --- 1. Speech-to-Text ---
        transcription_result = await self.stt.transcribe(audio_data)
        if not transcription_result:
            logger.info(f"STT produced no text for participant {participant_id}. Ending pipeline.")
            return

        original_text, confidence = transcription_result
        logger.info(f"STT successful for participant {participant_id}: '{original_text}'")

        audio_segment_id = str(uuid4())

        # Publish transcription event immediately for real-time UI updates
        if original_text:
            await self.events_producer.publish_event(
                "transcription.completed",
                TranscriptionEvent(
                    segment_id=audio_segment_id,
                    call_id=call_id,
                    participant_id=participant_id,
                    text=original_text,
                    confidence=confidence,
                )
            )

        # # --- 2. Translation ---
        # # Translate only if a source language is provided and differs from the target language
        # translated_text = await self.translation.translate(
        #     original_text, self.source_language, self.target_language
        # )

        # if translated_text:
        #     logger.info(f"Translation from '{self.source_language}' to '{self.target_language}' successful: '{translated_text}'")
        #     text_for_tts = translated_text
        #     await self._publish_event(
        #         settings.kafka_topic_translations,
        #         TranslationEvent(
        #             call_id=call_id,
        #             participant_id=participant_id,
        #             original_text=original_text,
        #             translated_text=translated_text,
        #             source_language=self.source_language,
        #             target_language=self.target_language
        #         )
        #     )
        # else:
        #     logger.warning(f"Translation failed for text: '{original_text}'. Halting translation pipeline.")
        #     return

        # # --- 3. Text-to-Speech ---
        # synthesized_audio = await self.tts.synthesize(text_for_tts)

        # if synthesized_audio:
        #     logger.info(f"TTS successful, generated {len(synthesized_audio)} bytes of audio.")

        #     # TODO: Return Synthesized Audio to Voice Router
        # else:
        #     logger.error(f"TTS failed for text: '{text_for_tts}'")
