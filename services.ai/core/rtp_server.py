import asyncio
import struct
from typing import Callable

from cortexacommon.logging import get_logger

logger = get_logger(__name__)

# Callback type for handling received packets
# Arguments are: ssrc (int), payload (bytes)
RtpPacketHandler = Callable[[int, bytes], None]


class RtpServerProtocol(asyncio.DatagramProtocol):
    """
    An asyncio DatagramProtocol for receiving and robustly parsing RTP packets.
    It correctly handles RTP header extensions, CSRC lists, and padding,
    while filtering out non-RTP (e.g., RTCP) packets.
    """

    def __init__(self, packet_handler: RtpPacketHandler):
        self.packet_handler = packet_handler
        self.transport = None

    def connection_made(self, transport: asyncio.DatagramTransport) -> None:
        self.transport = transport
        logger.info(f"RTP UDP server listening on {transport.get_extra_info('sockname')}")

    def datagram_received(self, data: bytes, addr: tuple[str, int]) -> None:
        """
        Called by the asyncio event loop when a UDP packet is received.
        Parses the RTP header to extract the SSRC and the raw codec payload.
        """
        if len(data) < 12:
            return  # Not a valid RTP packet

        try:
            # --- Header Parsing ---
            # First byte: Version (V), Padding (P), Extension (X), CSRC count (CC)
            b0 = data
            version = (b0 >> 6) & 0x03
            if version!= 2:
                return  # Not an RTP version 2 packet

            has_padding = (b0 >> 5) & 0x01
            has_extension = (b0 >> 4) & 0x01
            csrc_count = b0 & 0x0F

            # Second byte: Marker (M) and Payload Type (PT)
            b1 = data[1]
            payload_type = b1 & 0x7F

            # Filter out RTCP packets (Payload Types 200-204 are common for RTCP)
            if 200 <= payload_type <= 204:
                return

            # SSRC is always at bytes 8-11
            (ssrc,) = struct.unpack("!I", data[8:12])

            # --- Payload Extraction ---
            # Calculate the header length to find where the payload begins
            header_length = 12 + (4 * csrc_count)
            if len(data) < header_length:
                return # Malformed packet

            # Account for header extension if present
            if has_extension:
                if len(data) < header_length + 4:
                    return # Malformed packet
                ext_header_len_words, = struct.unpack("!H", data[header_length + 2 : header_length + 4])
                header_length += 4 + (ext_header_len_words * 4)
                if len(data) < header_length:
                    return # Malformed packet

            # Account for padding at the end
            padding_length = data[-1] if has_padding and len(data) > 0 else 0
            if padding_length > len(data) - header_length:
                return # Malformed padding

            payload = data[header_length : len(data) - padding_length]
            if not payload:
                return

            self.packet_handler(ssrc, payload)

        except struct.error:
            # This can happen with malformed packets, safely ignore.
            return
        except Exception as e:
            logger.error(f"Error processing datagram from {addr}: {e}", exc_info=True)

    def error_received(self, exc: Exception) -> None:
        logger.error(f"RTP UDP server error: {exc}")

    def connection_lost(self, exc: Exception | None) -> None:
        logger.info("RTP UDP server closed.")