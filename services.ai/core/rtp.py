import asyncio
import av

from cortexacommon.logging import get_logger

from.s2st import S2STProcessor

logger = get_logger(__name__)

# --- Constants ---
SAMPLE_RATE = 16000
# More sensible defaults for better transcription quality
SILENCE_THRESHOLD_FRAMES = 25  # 25 frames * 30ms/frame = 750ms of silence
MIN_AUDIO_BYTES = int(SAMPLE_RATE * 2 * 1.0)  # Minimum 1.0 second of audio to transcribe


class RTCAudioProcessor:
    """
    Processes a raw audio payload by decoding it from Opus, resampling it,
    detecting voice activity, buffering speech segments, and passing them to
    the S2STProcessor for transcription.
    """

    def __init__(self, processor: S2STProcessor, participant_id: str, call_id: str):
        self.processor = processor
        self.participant_id = participant_id
        self.call_id = call_id
        self._opus_decoder = av.CodecContext.create('opus', 'r')
        self._resampler = av.AudioResampler(format="s16", layout="mono", rate=SAMPLE_RATE)
        self._audio_buffer = bytearray()
        # Buffer incoming PCM to exact VAD frame boundaries for accuracy
        self._vad_frame_bytes = self.processor.vad.frame_size * 2  # e.g., 30ms @ 16kHz/16-bit => 960 bytes
        self._vad_buffer = bytearray()
        self._is_speaking = False
        self._silence_frames = 0
        self._is_active = False
        self._task_queue = asyncio.Queue(maxsize=100) # Add a maxsize to prevent memory bloat
        self._processing_task: asyncio.Task | None = None

    async def _run_processing_loop(self) -> None:
        """The main loop that processes audio frames from the internal queue."""
        logger.info(f"Starting audio processing loop for participant {self.participant_id}")
        while self._is_active:
            try:
                payload = await self._task_queue.get()
                if payload is None:  # Sentinel value to stop the loop
                    break

                # Each payload is a single Opus packet. Decode it directly.
                try:
                    packet = av.Packet(payload)
                    frames = self._opus_decoder.decode(packet)
                    if not frames:
                        continue # Decoder might need more data; this is normal.

                    for frame in frames:
                        resampled_frames = self._resampler.resample(frame)
                        for resampled_frame in resampled_frames:
                            frame_data = resampled_frame.to_ndarray().tobytes()
                            await self._handle_vad(frame_data)
                except Exception as e:
                    logger.warning(f"Opus decoding error for participant {self.participant_id}: {e}")
                    continue

            except asyncio.CancelledError:
                logger.info(f"Audio processing loop cancelled for participant {self.participant_id}")
                break
            except Exception as e:
                logger.error(f"Error in audio processor for participant {self.participant_id}: {e}", exc_info=True)

        logger.info(f"Stopping audio processing loop for participant {self.participant_id}")
        if len(self._audio_buffer) > MIN_AUDIO_BYTES:
            await self._trigger_pipeline()

    def process_payload(self, payload: bytes) -> None:
        """
        Receives a raw RTP payload from the network layer and puts it in a queue
        for the background processing task.
        """
        if self._is_active:
            try:
                self._task_queue.put_nowait(payload)
            except asyncio.QueueFull:
                logger.warning(f"Audio processing queue full for participant {self.participant_id}. Dropping packet.")

    async def _handle_vad(self, frame_data: bytes) -> None:
        """
        Accumulate resampled PCM and run VAD on fixed-size frames to ensure accuracy.
        """
        self._vad_buffer.extend(frame_data)

        # Process audio in perfectly sized chunks for the VAD
        while len(self._vad_buffer) >= self._vad_frame_bytes:
            chunk = self._vad_buffer[:self._vad_frame_bytes]
            del self._vad_buffer[:self._vad_frame_bytes]

            is_speech = self.processor.vad.is_speech(chunk)

            if is_speech:
                self._is_speaking = True
                self._silence_frames = 0
                self._audio_buffer.extend(chunk)
            elif self._is_speaking:
                # We were speaking, but now there's silence.
                # Continue buffering for a moment to catch trailing words.
                self._audio_buffer.extend(chunk)
                self._silence_frames += 1
                if self._silence_frames > SILENCE_THRESHOLD_FRAMES:
                    await self._trigger_pipeline()
                    self._is_speaking = False

    async def _trigger_pipeline(self) -> None:
        if len(self._audio_buffer) < MIN_AUDIO_BYTES:
            self._audio_buffer.clear()
            return

        audio_to_process = bytes(self._audio_buffer)
        self._audio_buffer.clear()

        logger.info(f"Sending {len(audio_to_process)} bytes of audio for transcription from {self.participant_id}.")
        asyncio.create_task(
            self.processor.start_pipeline_process(
                audio_data=audio_to_process,
                participant_id=self.participant_id,
                call_id=self.call_id
            )
        )

    def start(self) -> None:
        """Starts the audio processing task in the background."""
        if not self._is_active:
            self._is_active = True
            self._processing_task = asyncio.create_task(self._run_processing_loop())

    def stop(self) -> None:
        """Stops the audio processing task."""
        if self._is_active:
            self._is_active = False
            if self._processing_task:
                self._task_queue.put_nowait(None)
                self._processing_task.cancel()
                self._processing_task = None