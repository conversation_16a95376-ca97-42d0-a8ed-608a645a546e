import asyncio

from cortexacommon.logging import get_logger

from.rtp import RTCAudioProcessor
from.s2st import S2STProcessor

logger = get_logger(__name__)


class RtpStreamManager:
    """
    Manages RTP streams for a single call, mapping SSRC to audio processors.
    """

    def __init__(self, call_id: str, s2st_processor: S2STProcessor):
        self.call_id = call_id
        self._s2st_processor = s2st_processor
        self._ssrc_map: dict[int, str] = {}  # SSRC -> participant_id
        self._processors: dict = {}  # participant_id -> processor
        self._transports: list = []

    def register_transport(self, transport: asyncio.DatagramTransport):
        self._transports.append(transport)

    def register_stream(self, ssrc: int, participant_id: str, role: str):
        """
        Maps an SSRC to a participant and creates an audio processor for them.
        """
        logger.info(f"Registering stream for call {self.call_id}: SSRC {ssrc} -> participant {participant_id} ({role})")
        self._ssrc_map[ssrc] = participant_id
        if participant_id not in self._processors:
            processor = RTCAudioProcessor(
                processor=self._s2st_processor,
                participant_id=participant_id,
                call_id=self.call_id
            )
            processor.start()
            self._processors[participant_id] = processor

    def handle_rtp_packet(self, ssrc: int, payload: bytes):
        """
        Finds the correct audio processor for an SSRC and gives it the audio payload.
        """
        participant_id = self._ssrc_map.get(ssrc)
        if not participant_id:
            logger.warning(f"Received RTP packet with unknown SSRC {ssrc} for call {self.call_id}")
            return

        processor = self._processors.get(participant_id)
        if processor:
            # This is where the audio data enters the processing pipeline
            processor.process_payload(payload)
        else:
            logger.warning(f"No processor found for participant {participant_id} (SSRC {ssrc})")

    def stop(self):
        """
        Stops all processors and closes all associated UDP transports.
        """
        logger.info(f"Stopping all streams for call {self.call_id}")
        for processor in self._processors.values():
            processor.stop()
        for transport in self._transports:
            transport.close()
        self._processors.clear()
        self._ssrc_map.clear()
        self._transports.clear()


# A global registry to hold managers for all active calls
# In a real production system, this might be backed by Redis or another store
# to handle multiple service replicas.
CALL_MANAGERS: dict = {}
