# from core.stream_manager import CALL_MANAGERS

# class CallEndedConsumer(Consumer):
#     #...
#     async def handle_event(self, context: ApplicationContext, payload: dict[str, Any]) -> None:
#         call_id = payload.get("call_id")
#         logger.info(f"Received call ended event for call: {call_id}")

#         if call_id in CALL_MANAGERS:
#             manager = CALL_MANAGERS[call_id]
#             manager.stop()
#             del CALL_MANAGERS[call_id]
#             logger.info(f"Cleaned up resources for call {call_id}")
#         else:
#             logger.warning(f"No active stream manager found for ended call {call_id}")