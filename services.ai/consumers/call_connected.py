import asyncio
from typing import Any

from cortexacommon.logging import get_logger
from cortexacommon.events.schemas import CallConnectedEvent

from .base import Consumer
from context import ApplicationContext
from config import settings
from core.rtp_server import RtpServerProtocol
from core.stream_manager import RtpStream<PERSON>anager, CALL_MANAGERS

TOPIC = "call-orchestrator.call-connected"

logger = get_logger(__name__)


class CallConnectedConsumer(Consumer):
    def __init__(self):
        super().__init__(TOPIC)

    async def handle_event(self, context: ApplicationContext, payload: dict[str, Any]) -> None:
        event = CallConnectedEvent(**payload)
        logger.info(f"Received session ready for forking event: {event}")

        if not context.s2st_processor or not context.voice_router_client:
            logger.error("S2ST Processor or Voice Router Client not initialized.")
            return

        # Create and store a manager for this call
        stream_manager = RtpStreamManager(event.call_id, context.s2st_processor)
        CALL_MANAGERS[event.call_id] = stream_manager

        try:
            # --- Step 1: Create UDP listeners for caller and operator ---
            loop = asyncio.get_running_loop()
            
            caller_transport, _ = await loop.create_datagram_endpoint(
                lambda: RtpServerProtocol(packet_handler=stream_manager.handle_rtp_packet),
                local_addr=('0.0.0.0', 0)
            )
            stream_manager.register_transport(caller_transport)
            caller_addr = caller_transport.get_extra_info('sockname')
            logger.info(f"Caller RTP listener created at {caller_addr}")

            operator_transport, _ = await loop.create_datagram_endpoint(
                lambda: RtpServerProtocol(packet_handler=stream_manager.handle_rtp_packet),
                local_addr=('0.0.0.0', 0)
            )
            stream_manager.register_transport(operator_transport)
            operator_addr = operator_transport.get_extra_info('sockname')
            logger.info(f"Operator RTP listener created at {operator_addr}")

            # --- Step 2: Call Voice Router to start forking ---
            ai_service_ip = settings.service_ip
            ssrc_mapping = await context.voice_router_client.attach(
                media_session_id=event.media_session_id,
                ip=ai_service_ip,
                caller_port=caller_addr[1],
                operator_port=operator_addr[1]
            )
            logger.info(f"SSRC mapping received: {ssrc_mapping}")

            # --- Step 3: Register the SSRC mappings ---
            for mapping in ssrc_mapping:
                participant_id = event.operator_id if mapping['label'] == 'operator' else event.call_id
                stream_manager.register_stream(
                    ssrc=mapping['ssrc'],
                    participant_id=participant_id,
                    role=mapping['label']
                )
            
            logger.info(f"Successfully attached to media session {event.media_session_id}")

        except Exception as e:
            logger.error(f"Failed to attach to media session {event.media_session_id}: {e}", exc_info=True)
            stream_manager.stop()
            if event.call_id in CALL_MANAGERS:
                del CALL_MANAGERS[event.call_id]
