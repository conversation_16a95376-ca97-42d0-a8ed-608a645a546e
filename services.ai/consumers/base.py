from typing import Any

from cortexacommon.events.consumer import EventConsumer

from context import ApplicationContext

# TODO: This wrapper should be in cortexa-common
# and the consumer should be easier to init

class Consumer:
    def __init__(self, topic: str):
        self.topic = topic

    def setup(self, event_consumer: EventConsumer, context: ApplicationContext) -> None:
        async def _handler(payload: dict[str, Any]) -> None:
            await self.handle_event(context, payload)
        event_consumer.register_handler(self.topic, _handler)

    async def handle_event(self, context: ApplicationContext, payload: dict[str, Any]) -> None:
        raise NotImplementedError
