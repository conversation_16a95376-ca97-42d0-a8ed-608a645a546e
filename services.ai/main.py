import asyncio

from cortexacommon.logging import get_logger

from context import ApplicationContext
from consumers.call_connected import CallConnectedConsumer

logger = get_logger(__name__)


async def run() -> None:
    logger.info("Starting AI service")

    consumers = [
        CallConnectedConsumer()
    ]

    context = await ApplicationContext.create([c.topic for c in consumers])

    # Setup event consumers
    for consumer in consumers:
        consumer.setup(event_consumer=context.event_consumer, context=context)

    # Start Kafka consumer loop
    await context.event_consumer.start()
    consume_task = asyncio.create_task(context.event_consumer.consume())

    logger.info("AI service started successfully")

    try:
        await asyncio.Event().wait()
    except KeyboardInterrupt:
        pass
    finally:
        await context.cleanup()
        consume_task.cancel()


if __name__ == "__main__":
    asyncio.run(run())
