import httpx

from cortexacommon.logging import get_logger

logger = get_logger(__name__)

class VoiceRouterClient:
    def __init__(self, base_url: str):
        self._base_url = base_url.rstrip("/")
        self._client = httpx.AsyncClient(base_url=self._base_url)

    async def attach(self, media_session_id: str, ip: str, caller_port: int, operator_port: int) -> list:
        # POST body matches the internal route contract; voice-router will fork RTP to our UDP receivers.
        response = await self._client.post(f"/internal/sessions/{media_session_id}/attach", json={
            "targets": [
                {"label": "caller", "ip": ip, "port": caller_port},
                {"label": "operator", "ip": ip, "port": operator_port},
            ]
        }, timeout=5.0)

        response.raise_for_status()
        data = response.json()
        return data.get("ssrcMapping",)

    async def close(self):
        if self._client:
            await self._client.aclose()