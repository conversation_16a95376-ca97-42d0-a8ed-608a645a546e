import asyncio
from typing import Callable, <PERSON>waitable, <PERSON><PERSON>
from config import settings

from cortexacommon.logging import get_logger
from cortexacommon.events.consumer import EventConsumer
from cortexacommon.events.schemas import MediaProducersReadyEvent

logger = get_logger(__name__)

TOPIC = "voice-router.media.producers-ready"


async def setup_voice_participants_ready_consumer(handler: Callable[[str], Awaitable[None]]) -> Tuple[EventConsumer, asyncio.Task]:
    """
    Create, start, and begin consuming from the voice participants ready topic.

    Returns the EventConsumer instance and the background asyncio.Task running the consume loop,
    so callers can manage lifecycle (store on context and stop during shutdown).
    """
    events_consumer = EventConsumer(
        kafka_settings=settings.kafka_settings,
        topics=[TOPIC],
    )

    async def _handle_voice_participants_ready(payload: dict) -> None:
        event = MediaProducersReadyEvent(**payload)
        logger.info(f"Received voice participants ready event: {event}")
        await handler(event.media_session_id)

    events_consumer.register_handler(TOPIC, _handle_voice_participants_ready)

    logger.info("Starting voice participants ready consumer")
    await events_consumer.start()

    # Run the consume loop in the background
    consume_task = asyncio.create_task(events_consumer.consume(), name="voice-participants-ready-consumer")

    return events_consumer, consume_task
