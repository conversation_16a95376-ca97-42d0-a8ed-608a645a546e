from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict
from cortexacommon.config import KafkaSettings, DatabaseSettings, SecuritySettings


class Settings(BaseSettings):
    host: str = "0.0.0.0"
    port: int = 8002
    debug: bool = False
    service_name: str = "call-orchestrator"
    kafka_settings: KafkaSettings = KafkaSettings()
    database: DatabaseSettings = DatabaseSettings()
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    voice_router_url: str = "http://localhost:3001"
    sip_gateway_url: str = "http://localhost:8010"

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )


settings = Settings()
