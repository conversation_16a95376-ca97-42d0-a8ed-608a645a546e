from fastapi import FastAPI
from contextlib import asynccontextmanager

from config import settings
from context import ApplicationContext
from consumers.call_sip_received import setup_sip_call_received_consumer
from consumers.voice_participants_ready import setup_voice_participants_ready_consumer

from api.v1.call.router import router as call_router

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Initialize application context
    context = await ApplicationContext.get_instance()
    await context.initialize()

    consumer_tasks = []
    consumers = [
        setup_sip_call_received_consumer(context.call_handler.on_sip_call_received),
        setup_voice_participants_ready_consumer(context.call_handler.on_voice_participants_ready),
    ]

    for consumer_setup in consumers:
        events_consumer, consume_task = await consumer_setup
        consumer_tasks.append(consume_task)

    yield

    # Shutdown consumers
    for task in consumer_tasks:
        task.cancel()

    await context.cleanup()

app = FastAPI(
    title="Call Orchestrator Service",
    lifespan=lifespan,
    host=settings.host,
    port=settings.port,
)

app.include_router(call_router)