import asyncio

from cortexacommon.logging import get_logger

from config import settings
from core.db import Database
from services.sip_gateway import SIPGatewayService
from services.voice_router import VoiceRouterService
from services.call_handler import CallHandler

logger = get_logger(__name__)

class ApplicationContext:
    """
    Application context for managing shared resources.

    Manages Kafka consumers and external service clients, plus the database engine
    and session factory used by repositories/ORM. Also exposes the CallHandler
    wrapper that orchestrates call lifecycle.
    """

    _instance: 'ApplicationContext | None' = None
    _lock = asyncio.Lock()

    def __init__(self):
        self._sip_gateway_service: SIPGatewayService | None = None
        self._voice_router_service: VoiceRouterService | None = None

        self._call_handler: CallHandler | None = None
        self._db: Database | None = None

        self._initialized = False

    @classmethod
    async def get_instance(cls) -> 'ApplicationContext':
        if cls._instance is None:
            async with cls._lock:
                if cls._instance is None:
                    cls._instance = cls()
        return cls._instance

    @property
    def sip_gateway_service(self) -> SIPGatewayService:
        assert self._sip_gateway_service is not None, "SIP gateway service not initialized"
        return self._sip_gateway_service

    @property
    def voice_router_service(self) -> VoiceRouterService:
        assert self._voice_router_service is not None, "Voice router service not initialized"
        return self._voice_router_service

    @property
    def database(self) -> Database:
        assert self._db is not None, "Database not initialized"
        return self._db

    @property
    def call_handler(self) -> CallHandler:
        assert self._call_handler is not None, "Call handler not initialized"
        return self._call_handler

    @property
    def is_initialized(self) -> bool:
        return self._initialized

    async def initialize(self) -> None:
        if self._initialized:
            return
        try:
            self._db = Database(settings.database)
            await self._db.initialize()

            logger.info(f"Settings are: {settings}")
            self._sip_gateway_service = SIPGatewayService(settings.sip_gateway_url)
            self._voice_router_service = VoiceRouterService(settings.voice_router_url)

            self._call_handler = CallHandler(
                db=self._db,
                sip_gateway=self._sip_gateway_service,
                voice_router=self._voice_router_service,
            )

            await self._call_handler.initialize()

            self._initialized = True
        except Exception as e:
            raise RuntimeError(f"Failed to initialize application context: {e}")

    async def cleanup(self) -> None:
        try:
            if self._db:
                await self._db.cleanup()
                self._db = None

            if self._call_handler:
                await self._call_handler.cleanup()
                self._call_handler = None

            self._initialized = False
        except Exception as e:
            print(f"Error during application context cleanup: {e}")
