import httpx

from cortexacommon.logging import get_logger

logger = get_logger(__name__)


class VoiceRouterService:
    """
    Client for the Voice Router service.
    """

    def __init__(self, base_url: str = "http://localhost:3001"):
        """
        Initialize the service.

        Args:
            base_url: Base URL for the Voice Router HTTP API (e.g., http://voice-router:3001)
        """
        self._base_url = base_url.rstrip("/")
        self._client = httpx.AsyncClient(base_url=self._base_url)

    async def create_media_session(self, timeout: float = 5.0) -> str:
        """
        Create a new media session.

        Calls POST /internal/sessions and returns the mediaSessionId from the response.

        Args:
            timeout: Request timeout in seconds.

        Returns:
            The created media session ID.
        """
        response = await self._client.post(
            "/internal/sessions",
            json={},
            timeout=timeout
        )

        logger.info(f"create_media_session response: {response.text}")
        response.raise_for_status()
        

        response_data = response.json()
        return response_data["mediaSessionId"]

    async def delete_media_session(self, media_session_id: str, timeout: float = 5.0) -> None:
        """
        Deletes a specific media session.

        Calls DELETE /internal/sessions/{media_session_id} which returns a 204 No Content.

        Args:
            media_session_id: The ID of the session to delete.
            timeout: Request timeout in seconds.
        """
        response = await self._client.delete(
            f"/internal/sessions/{media_session_id}",
            timeout=timeout
        )

        response.raise_for_status()


    async def close(self):
        """Cleanly closes the underlying HTTP client and its connections."""
        await self._client.aclose()
