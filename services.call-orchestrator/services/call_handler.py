from core.db import Database
from core.repositories import CallRepository
from typing import Awaitable, Callable, TypeVar

from cortexacommon.logging import get_logger
from cortexacommon.events.producer import EventProducer
from cortexacommon.events.schemas import (
    CallStartedEvent,
    CallConnectingEvent,
    WebRTCConnectionDetailsEvent,
)

from config import settings
from .sip_gateway import SIPGatewayService
from .voice_router import VoiceRouterService


logger = get_logger(__name__)
T = TypeVar("T")

class CallHandler:
    """
    High-level wrapper that orchestrates call lifecycle using repositories and services.
    """

    def __init__(
        self,
        db: Database,
        sip_gateway: SIPGatewayService,
        voice_router: VoiceRouterService,
    ):
        self._db = db
        self._sip_gateway = sip_gateway
        self._voice_router = voice_router
        self._producer: EventProducer | None = None

    async def initialize(self) -> None:
        # Initialize a producer lazily to publish call events
        self._producer = EventProducer(settings.kafka_settings)
        await self._producer.start()

    async def cleanup(self) -> None:
        if self._producer:
            await self._producer.stop()
            self._producer = None

    async def _with_repo(self, op: Callable[[CallRepository], Awaitable[T]]) -> T:
        async with self._db.session_factory() as session:
            async with session.begin():
                repo = CallRepository(session)
                return await op(repo)

    async def _ensure_call_set_media_and_operator(
        self,
        repo: CallRepository,
        call_id: str,
        media_session_id: str,
        operator_id: str,
    ) -> None:
        await repo.create_if_not_exists(call_id)
        await repo.set_media_session(call_id, media_session_id)
        await repo.set_operator(call_id, operator_id)

    async def on_sip_call_received(self, call_id: str) -> None:
        """
        Handle incoming SIP call events.
        """
        assert self._producer, "Event producer not initialized"

        # Create call record
        await self._with_repo(lambda repo: repo.create_if_not_exists(call_id))

        # Publish event after commit
        event = CallStartedEvent(call_id=call_id)
        await self._producer.publish_event("call-orchestrator.call-started", event)

    async def accept_call(self, call_id: str, operator_id: str) -> None:
        # TODO: Turn this function into a SAGA transaction pattern
        assert self._producer, "Event producer not initialized"

        # Step 1: Create media session on Voice Router
        media_session_id = await self._voice_router.create_media_session()

        # Persist media_session_id and operator_id on the call right away
        await self._with_repo(lambda repo: self._ensure_call_set_media_and_operator(repo, call_id, media_session_id, operator_id))

        # Step 2: Publish operator WebRTC connection details (targeted to operator_id)
        # Include the Voice Router WS URL and the media_session_id; the operator UI
        # will connect and negotiate transports directly with the Voice Router.
        await self._producer.publish_dict(
            topic="webrtc.connection-details",
            data=WebRTCConnectionDetailsEvent(
                call_id=call_id,
                operator_id=operator_id,
                media_session_id=media_session_id,
            ).model_dump(),
            key=operator_id,
        )

        # Step 3: Command the SIP Gateway to connect the SIP leg
        await self._sip_gateway.connect_call(call_id, media_session_id)

        # Step 4: Update call status to ACTIVE and publish final event
        await self._with_repo(lambda repo: repo.set_status(call_id, "active"))

        await self._producer.publish_dict(
            topic="call-orchestration.call-connecting",
            data=CallConnectingEvent(call_id=call_id, operator_id=operator_id,
                                     media_session_id=media_session_id).model_dump(),
            key=call_id,
        )

    async def on_voice_participants_ready(self, media_session_id: str) -> None:
        """
        Handle media.producers-ready events.
        """
        assert self._producer, "Event producer not initialized"

        logger.info(f"Received media producers ready for media session {media_session_id}")
        call = await self._with_repo(lambda repo: repo.get_by_media_session_id(media_session_id))

        if not call:
            raise ValueError(f"Call not found for media session {media_session_id}")
        
        if not call.operator_id:
            raise ValueError(f"Operator not set for call {call.id}")

        event = CallConnectingEvent(call_id=call.id, operator_id=call.operator_id, media_session_id=media_session_id)
        await self._producer.publish_event("call-orchestrator.call-connected", event)
