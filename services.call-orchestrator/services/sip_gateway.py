import httpx

class SIPGatewayService:
    """
    Client for the SIP Gateway service.
    """

    def __init__(self, base_url: str = "http://localhost:8010"):
        """
        Initialize the service.

        Args:
            base_url: Base URL for the SIP Gateway HTTP API
        """
        self._base_url = base_url.rstrip("/")
        self._client = httpx.AsyncClient(base_url=self._base_url)

    async def connect_call(self, call_id: str, media_session_id: str, timeout: float = 5.0) -> None:
        """
        Connect a SIP call to a media session.

        Calls POST /internal/connect with the call and media session IDs in the payload.

        Args:
            call_id: The SIP call ID
            media_session_id: The media session ID
            timeout: Request timeout in seconds

        Raises:
            HTTPError: If the request fails
        """
        payload = {"call_id": call_id, "media_session_id": media_session_id}
    
        response = await self._client.post(
            "/internal/connect",
            json=payload,
            timeout=timeout
        )

        response.raise_for_status()

    async def close(self):
        """Cleanly close the client's connections."""
        await self._client.aclose()