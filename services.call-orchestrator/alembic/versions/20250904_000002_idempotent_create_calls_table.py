from __future__ import annotations

from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "20250904_000002"
down_revision = "20250904_000001"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """
    Create the calls table if it does not already exist.

    This guards against environments that were previously stamped to head without
    actually applying the initial migration, so running this migration will
    materialize the table without erroring if it's already present.
    """
    bind = op.get_bind()
    inspector = sa.inspect(bind)

    if "calls" not in inspector.get_table_names():
        op.create_table(
            "calls",
            sa.Column("id", sa.String(), primary_key=True, nullable=False),
            sa.Column("created_at", sa.DateTime(timezone=True), nullable=False),
            sa.Column("status", sa.String(), nullable=False, server_default="received"),
            sa.Column("media_session_id", sa.String(), nullable=True),
        )
        # helpful index on primary key for common lookups
        op.create_index("ix_calls_id", "calls", ["id"])  


def downgrade() -> None:
    bind = op.get_bind()
    inspector = sa.inspect(bind)

    if "calls" in inspector.get_table_names():
        op.drop_index("ix_calls_id", table_name="calls")
        op.drop_table("calls")

