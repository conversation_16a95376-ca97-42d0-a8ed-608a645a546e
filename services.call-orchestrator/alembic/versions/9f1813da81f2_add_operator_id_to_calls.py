"""
Revision ID: 9f1813da81f2
Revises: 20250904_000002
Create Date: 2025-09-12 12:23:49.234589

"""
from __future__ import annotations

from alembic import op
import sqlalchemy as sa
from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = '9f1813da81f2'
down_revision: Union[str, None] = '20250904_000002'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('calls', sa.Column('operator_id', sa.String(), nullable=True))
    op.drop_index(op.f('ix_calls_id'), table_name='calls')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index(op.f('ix_calls_id'), 'calls', ['id'], unique=False)
    op.drop_column('calls', 'operator_id')
    # ### end Alembic commands ###

