import uuid
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from .models import Call


class CallRepository:
    def __init__(self, session: AsyncSession):
        self._session = session

    async def get(self, call_id: str) -> Call | None:
        result = await self._session.execute(select(Call).where(Call.id == call_id))
        return result.scalar_one_or_none()

    async def create_if_not_exists(self, call_id: str) -> Call:
        existing = await self.get(call_id)
        if existing:
            return existing
        call = Call(id=call_id)
        self._session.add(call)
        await self._session.flush()
        return call

    async def set_media_session(self, call_id: str, media_session_id: str) -> None:
        await self._session.execute(
            update(Call).where(Call.id == call_id).values(media_session_id=media_session_id)
        )

    async def set_status(self, call_id: str, status: str) -> None:
        await self._session.execute(
            update(Call).where(Call.id == call_id).values(status=status)
        )

    async def set_operator(self, call_id: str, operator_id: str) -> None:
        await self._session.execute(
            update(Call).where(Call.id == call_id).values(operator_id=operator_id)
        )

    async def get_by_media_session_id(self, media_session_id: str) -> Call | None:
        result = await self._session.execute(
            select(Call).where(Call.media_session_id == media_session_id))
        return result.scalars().first()
