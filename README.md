# Cortexa

A real-time, AI-powered assistant for emergency call centers

## Overview

Cortexa's core mission is to reduce response times, improve accuracy in information gathering, and break down language barriers, ultimately leading to better outcomes for callers in distress. To achieve this, Cortexa augments the capabilities of human operators by providing instant transcription, speech-to-speech translation for non-native speakers, and intelligent assistance features. The system is built on a scalable microservice architecture to ensure high availability and low latency, which are critical in emergency response scenarios.

## Core Features

- **Real-Time Transcription:** Live, streaming transcription of dialogue from both the caller and the operator.

- **Speech-to-Speech Translation (S2ST):** Near real-time translation for calls where a language barrier exists. The operator can hear the caller's speech translated into their native language.

- **Call Management:** A complete system for queuing, assigning, and managing the lifecycle of incoming emergency calls.

## Stakeholders

The application will be used by three stakeholders:

1. Call Operator: The call operator talks with the caller to understand the emergency to create a case file that is later handed to a Dispatcher to assign ground resources to help those in need. They must efficiently understand the emergency to accurately describe the problem.

2. Call Translator: Someone who is able to translate on behalf of the caller so that the operator can understand the situation via the language barrier. The Translator will sit on standby similar to the operator.

3. Team Manager: The manager is responsible for a small team of Call Operators and watches over at a high level. They provide oversight and assistance on request.

## Get Started

To get started with the project, you will need to install Docker and Docker Compose.

To bring up Supabase and our stack, run:

```
make start-dev
```


# TODO

1. Detect caller language before transcription
2. Publish audio back to operator
3. Refactor context to use FastAPI's state instead of Singleton
4. Refactor EventProducer to not require topics on init, only on register.
5. Add `silero-vad`
6. Cleanup Event names (remove service name prefix)
7. Add health checks to each container