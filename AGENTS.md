# Cortexa

## Summary

Cortexa's core mission is to reduce response times, improve accuracy in information gathering, and break down language barriers, ultimately leading to better outcomes for callers in distress. To achieve this, Cortexa augments the capabilities of human operators by providing instant transcription, speech-to-speech translation for non-native speakers, and intelligent assistance features. The system is built on a scalable microservice architecture to ensure high availability and low latency, which are critical in emergency response scenarios.

## Services

The monorepo contains the following services:

- **User Management Service:** Manages user roles and creation via Supabase.
- **Participant Presence Service:** Tracks the availability of operators and translators.
- **Call Orchestrator Service:** Assigns calls to operators and manages the call lifecycle.
- **Queuing Service:** Manages the queue of incoming calls and assigns them to operators using knowledge of operator availability.
- **WebSocket Bridge Service:** Bridges Kafka events to WebSocket connections.
- **Voice Router Service:** Provides a WebRTC SFU for voice communication.
- **AI Service:** Provides real-time transcription, translation, and TTS.
- **SIP Gateway Service:** Bridges external connections to the WebRTC network and triggers the call

## Development

The services are deployed Docker Compose and external HTTP requests are managed by Traefik.
The Makefile has helper commands that brings up the entire stack, including Supabase.