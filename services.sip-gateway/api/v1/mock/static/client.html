<!DOCTYPE html>
<html>

<head>
    <title>SIP Gateway Test Client</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="/mock/ui/mediasoup-client.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background-color: #f0f2f5;
        }
        .container {
            display: flex;
            flex-direction: column;
            align-items: center;
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            width: 90%;
            max-width: 400px;
        }
        h1 {
            margin-top: 0;
            color: #333;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .status-box {
            margin-top: 1.5rem;
            width: 100%;
            padding: 1rem;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .status-box p {
            margin: 0.5rem 0;
            font-family: 'Courier New', Courier, monospace;
            word-wrap: break-word;
        }
        audio {
            margin-top: 1rem;
            width: 100%;
            border-radius: 8px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>SIP Gateway Test Client</h1>
        <button id="callButton" onclick="mockSipCall()">Mock SIP Incoming Call</button>
        <div class="status-box">
            <p id="callId">Call ID: (none)</p>
            <p id="status">Status: Idle</p>
        </div>
        <audio id="audioPlayer" controls autoplay playsinline></audio>
    </div>

    <script>
        // Configuration
        const mediaRouterHttpUrl = 'http://localhost/voice';
        // The WebSocket URL is derived from the HTTP URL
        const mediaRouterWsUrl = mediaRouterHttpUrl.replace(/^http/, 'ws');

        // DOM Elements
        const callButton = document.getElementById('callButton');
        const callIdEl = document.getElementById('callId');
        const statusEl = document.getElementById('status');
        const audioPlayer = document.getElementById('audioPlayer');

        // Mediasoup & Connection State
        let device;
        let sendTransport;
        let recvTransport;
        let ws;
        let nextRequestId = 1;
        const pendingRequests = new Map();


        // --- Utils ---
        function uuidv4() {
            if (window.crypto && window.crypto.randomUUID) {
                return window.crypto.randomUUID();
            }
        }

        // --- UI & Status Functions ---
        function updateStatus(text) {
            console.log(text);
            statusEl.innerHTML = `Status: ${text}`;
        }

        // --- WebSocket Communication Wrapper ---
        /**
         * Sends a request to the server over WebSocket and returns a Promise
         * that resolves with the response.
         * @param {string} method - The method name to call on the server.
         * @param {object} data - The payload for the method.
         */
        function sendWsRequest(method, data = {}) {
            return new Promise((resolve, reject) => {
                if (!ws || ws.readyState !== WebSocket.OPEN) {
                    return reject(new Error('WebSocket is not connected.'));
                }
                const requestId = nextRequestId++;
                pendingRequests.set(requestId, { resolve, reject });

                const message = {
                    type: 'request',
                    id: requestId,
                    method,
                    data
                };
                ws.send(JSON.stringify(message));

                // Timeout to prevent memory leaks for unanswered requests
                setTimeout(() => {
                    if (pendingRequests.has(requestId)) {
                        pendingRequests.get(requestId).reject(new Error(`Request ${requestId} timed out`));
                        pendingRequests.delete(requestId);
                    }
                }, 10000);
            });
        }

        // --- Call Flow Logic ---

        // 1. User clicks the "Mock SIP Call" button
        async function mockSipCall() {
            callButton.disabled = true;
            callId = uuidv4();
            updateStatus('Initiating call...');
            try {
                // Call our mock gateway to trigger the Kafka event with a random uuid for the call
                const response = await fetch('/webhook',
                    {
                        method: 'POST',
                        body: JSON.stringify({ call_id: callId }),
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                if (!response.ok) throw new Error(`Server error: ${response.statusText}`);

                const data = await response.json();
                callIdEl.innerHTML = `Call ID: ${callId}`;
                updateStatus('Ringing...');

                // Start polling the mock gateway to see when the main app has connected the call
                await pollForConnection(callId);
            } catch (error) {
                updateStatus(`Error: ${error.message}`);
                callButton.disabled = false;
            }
        }

        // 2. Poll the mock gateway until the media session is ready
        async function pollForConnection(callId) {
            try {
                const response = await fetch(`/mock/ui/poll/${callId}`);
                const data = await response.json();

                if (data.status !== 'connected') {
                    setTimeout(() => pollForConnection(callId), 2000);
                    return;
                }

                updateStatus('Call connected. Setting up media...');
                // Once connected, start the mediasoup connection process with the media router
                await connectToMediaSession(data.media_session_id);

            } catch (error) {
                updateStatus(`Polling error: ${error.message}`);
                callButton.disabled = false;
            }
        }

        // 3. Connect to the media router (HTTP for caps, then WebSocket for signaling)
        async function connectToMediaSession(mediaSessionId) {
            try {
                // --- Step A: Fetch Router Capabilities via HTTP (one-time) ---
                updateStatus('Fetching router capabilities...');
                const response = await fetch(`${mediaRouterHttpUrl}/sessions/${mediaSessionId}/router-rtp-capabilities`);
                if (!response.ok) throw new Error('Could not fetch router capabilities.');
                const routerRtpCapabilities = await response.json();

                // --- Step B: Initialize Mediasoup Device ---
                device = new mediasoup.Device();
                await device.load({ routerRtpCapabilities });
                updateStatus('Device loaded.');

                // --- Step C: Establish WebSocket Connection ---
                updateStatus('Connecting to media server...');
                ws = new WebSocket(`${mediaRouterWsUrl}/ws?sessionId=${mediaSessionId}`);

                ws.onopen = () => {
                    updateStatus('Signaling connection established.');
                    // Connection is open, now create transports
                    createTransportsAndProduce();
                };

                ws.onmessage = (event) => {
                    const message = JSON.parse(event.data);

                    if (message.type === 'response') {
                        // Handle response to a request we sent
                        const promise = pendingRequests.get(message.id);
                        if (promise) {
                            if (message.error) {
                                promise.reject(new Error(message.error));
                            } else {
                                promise.resolve(message.data);
                            }
                            pendingRequests.delete(message.id);
                        }
                    } else if (message.type === 'notification') {
                        // Handle server-pushed notifications
                        handleWsNotification(message);
                    }
                };

                ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    updateStatus('WebSocket connection error.');
                };

                ws.onclose = () => {
                    updateStatus('WebSocket connection closed.');
                    callButton.disabled = false;
                };

            } catch (error) {
                updateStatus(`Media setup error: ${error.message}`);
                callButton.disabled = false;
            }
        }

        // 4. Create transports and start sending audio
        async function createTransportsAndProduce() {
            try {
                // --- Create Send Transport ---
                updateStatus('Creating send transport...');
                const sendTransportParams = await sendWsRequest('createWebRtcTransport');
                sendTransport = device.createSendTransport(sendTransportParams);

                sendTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
                    try {
                        await sendWsRequest('connectWebRtcTransport', { transportId: sendTransport.id, dtlsParameters });
                        callback();
                    } catch (e) { errback(e); }
                });

                sendTransport.on('produce', async ({ kind, rtpParameters, appData }, callback, errback) => {
                    try {
                        const { id } = await sendWsRequest('produce', { transportId: sendTransport.id, kind, rtpParameters, appData });
                        callback({ id });
                    } catch (e) { errback(e); }
                });

                // --- Create Recv Transport ---
                updateStatus('Creating receive transport...');
                const recvTransportParams = await sendWsRequest('createWebRtcTransport');
                recvTransport = device.createRecvTransport(recvTransportParams);

                recvTransport.on('connect', async ({ dtlsParameters }, callback, errback) => {
                     try {
                        await sendWsRequest('connectWebRtcTransport', { transportId: recvTransport.id, dtlsParameters });
                        callback();
                    } catch (e) { errback(e); }
                });

                // --- Get Mic and Produce ---
                updateStatus('Requesting microphone access...');
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                const track = stream.getAudioTracks()[0];
                // Mark this producer as the SIP caller so Voice Router can fork to AI
                await sendTransport.produce({ track, encodings: [{ maxBitrate: 64000 }], appData: { role: 'caller' } });
                updateStatus('Microphone is live. Sending audio.');

                // Initially, we might miss a notification if the SIP side connects instantly.
                // A robust solution could fetch existing producers here, but for this test,
                // we'll rely on the 'newProducer' notification.
                updateStatus('Ready to receive audio from SIP call...');

            } catch (error) {
                updateStatus(`Transport creation failed: ${error.message}`);
                console.error(error);
                if (ws) ws.close();
            }
        }

        // 5. Handle notifications from the server (e.g., new audio stream available)
        function handleWsNotification(notification) {
            const { method, data } = notification;
            switch (method) {
                case 'newProducer':
                    updateStatus('New producer detected (incoming SIP audio). Consuming...');
                    consumeNewProducer(data.producerId);
                    break;
                // Add other notification handlers here (e.g., producerClosed)
                default:
                    console.warn('Unknown notification method:', method);
                    break;
            }
        }

        async function consumeNewProducer(producerId) {
            try {
                const consumerParams = await sendWsRequest('consume', {
                    producerId,
                    transportId: recvTransport.id,
                    rtpCapabilities: device.rtpCapabilities
                });

                const consumer = await recvTransport.consume(consumerParams);
                const { track } = consumer;

                const stream = new MediaStream();
                stream.addTrack(track);
                audioPlayer.srcObject = stream;
                updateStatus('Connected! You should be able to talk and hear audio.');

            } catch (error) {
                updateStatus(`Failed to consume audio: ${error.message}`);
                console.error(error);
            }
        }

    </script>
</body>

</html>

